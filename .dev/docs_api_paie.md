# 📚 Documentation Complète de l'API SIRH Gestion de Paie

## Table des matières

- [Authentification](#authentification)
- [Entreprises](#entreprises)
- [Employés](#employés)
- [Paie & Bulletins](#paie--bulletins)
- [Prêts Employés](#prêts-employés)
- [Avances sur Salaire](#avances-sur-salaire)
- [Pointages](#pointages)
- [Absences](#absences)
- [Heures Supplémentaires](#heures-supplémentaires)
- [Paramètres & Barèmes](#paramètres--barèmes)
- [Santé & Utilitaires](#santé--utilitaires)

---

## Authentification

> ⚠️ **Note** : L'API actuelle ne nécessite pas d'authentification par défaut. À intégrer selon vos besoins (JWT, OAuth, etc).

---

## Entreprises

### Liste des entreprises

- **GET** `/api/entreprises`
- **Réponse :**
  ```json
{
    "success": true,
    "data": [
      {
        "id": "uuid",
        "raisonSociale": "LGI Consulting SARL",
        "adresse": "123 Avenue de Test, Tunis",
        "siret": "12345678901234",
        "codeNaf": "6202A"
      }
    ]
  }
```

### Créer une entreprise

- **POST** `/api/entreprises`
- **Body :**
  ```json
{
    "raisonSociale": "LGI Consulting SARL",
    "adresse": "123 Avenue de Test, Tunis",
    "siret": "12345678901234",
    "codeNaf": "6202A"
  }
```
- **Réponse :** `201 Created`

### Détail d'une entreprise

- **GET** `/api/entreprises/{id}`

### Liste des employés d'une entreprise

- **GET** `/api/entreprises/{id}/employes?actif=true`

### Statistiques de paie d'une entreprise

- **GET** `/api/entreprises/{id}/statistiques?annee=2024&mois=1`

---

## Employés

### Liste des employés

- **GET** `/api/employes?actif=true&entrepriseId=...`

### Créer un employé

- **POST** `/api/employes`
- **Body :**
  ```json
{
    "entrepriseId": "uuid",
    "nom": "Dupont",
    "prenom": "Jean",
    "fonction": "Développeur",
    "salaireBase": 1200,
    "dateEmbauche": "2023-01-15",
    "personnesACharge": 2,
    "telephone": "+216 20 123 456",
    "email": "<EMAIL>",
    "typeContrat": "CDI",
    "departement": "IT",
    "situationFamiliale": "MARIE"
  }
```

### Détail d'un employé

- **GET** `/api/employes/{id}`

### Modifier un employé

- **PUT** `/api/employes/{id}`

### Liste des bulletins d'un employé

- **GET** `/api/employes/{id}/bulletins?periode=2024-01`

### Générer un bulletin pour un employé

- **POST** `/api/employes/{id}/generer-bulletin`
- **Body :**
  ```json
{
    "periode": "2024-01",
    "sursalaire": 100,
    "anciennete": 50,
    "logementTransport": 100,
    "autres": 200,
    "autresRetenues": 0
  }
```

---

## Paie & Bulletins

### Calculer une paie (simulation)

- **POST** `/api/paie/calculer`
- **Body :**
  ```json
{
    "composants": {
      "salaireBase": 120000,
      "sursalaire": 0,
      "anciennete": 0,
      "logementTransport": 0,
      "autres": 0
    },
    "personnesACharge": 2,
    "periode": "2023-08",
    "autresRetenues": 0
  }
```
- **Réponse :**
  ```json
{
    "success": true,
    "data": {
      "revenuBrut": 120000,
      "cnss": 4800,
      "soldeApresCnss": 115200,
      "abattementProfessionnel": 32256,
      "soldeApresAbattement": 82944,
      "deductionsPersonnesCharge": 0,
      "baseImposable": 82944,
      "baseImposableArrondie": 82944,
      "irpp": 0,
      "totalRetenues": 4800,
      "netAPayer": 115200
    }
  }
```

### Générer et sauvegarder un bulletin

- **POST** `/api/paie/bulletins`
- **Body :** (voir ci-dessus)
- **Réponse :** `201 Created`

### Détail d'un bulletin

- **GET** `/api/paie/bulletins/{id}`

### Liste des bulletins (filtres & pagination)

- **GET** `/api/paie/bulletins?employeId=...&periode=2024-01&statut=VALIDE&page=1&limit=10`

### Modifier le statut d'un bulletin

- **PATCH** `/api/paie/bulletins/{id}/statut`
- **Body :** `{ "statut": "VALIDE" }`

### Exporter un bulletin en PDF

- **GET** `/api/paie/bulletins/{id}/pdf`
- **Réponse :** Fichier PDF

---

## Prêts Employés

### Liste des prêts

- **GET** `/api/prets?employeId=...`

### Détail d'un prêt

- **GET** `/api/prets/{id}`

### Échéances d'un prêt

- **GET** `/api/prets/{id}/echeances`

### Simulation de prêt

- **POST** `/api/prets/simuler`
- **Body :**
  ```json
{
    "montantInitial": 10000,
    "tauxInteret": 0.05,
    "dureeRemboursement": 24
  }
```

### Création d'un prêt

- **POST** `/api/prets`
- **Body :**
  ```json
{
    "employeId": "uuid",
    "montantInitial": 15000,
    "dureeRemboursement": 24,
    "tauxInteret": 0.05,
    "motif": "Achat voiture",
    "garantie": "Salaire"
  }
```

### Traitement d'un remboursement

- **POST** `/api/prets/{id}/remboursement`
- **Body :** `{ "montant": 500 }`

### Modifier le statut d'un prêt

- **PUT** `/api/prets/{id}/statut`
- **Body :** `{ "statut": "SUSPENDU" }`

---

## Avances sur Salaire

### Liste des avances

- **GET** `/api/avances?employeId=...`

### Détail d'une avance

- **GET** `/api/avances/{id}`

### Statut détaillé d'une avance

- **GET** `/api/avances/{id}/statut`

### Plan de remboursement

- **GET** `/api/avances/{id}/plan-remboursement`

### Demander une avance

- **POST** `/api/avances`
- **Body :**
  ```json
{
    "employeId": "uuid",
    "montant": 500,
    "motif": "Urgence familiale",
    "modeRemboursement": "UNIQUE",
    "nombreEcheances": 1
  }
```

### Approbation d'une avance

- **POST** `/api/avances/{id}/approuver`

### Traitement d'un remboursement

- **POST** `/api/avances/{id}/remboursement`
- **Body :** `{ "montant": 200 }`

### Annulation d'une avance

- **DELETE** `/api/avances/{id}`

---

## Pointages

### Liste des pointages

- **GET** `/api/pointages?employeId=...&dateDebut=2024-01-01&dateFin=2024-01-31`

### Ajouter un pointage

- **POST** `/api/pointages`
- **Body :**
  ```json
{
    "employeId": "uuid",
    "date": "2024-01-15",
    "heureArrivee": "08:30",
    "heureDepart": "17:00"
  }
```

---

## Absences

### Liste des absences

- **GET** `/api/absences?employeId=...&dateDebut=2024-01-01&dateFin=2024-01-31`

### Ajouter une absence

- **POST** `/api/absences`
- **Body :**
  ```json
{
    "employe_id": "uuid",
    "date_debut": "2024-01-10",
    "date_fin": "2024-01-12",
    "type_absence": "CONGE_PAYE",
    "motif": "Vacances",
    "impact_paie": false
  }
```

### Approbation d'une absence

- **POST** `/api/absences/{id}/approuver`

---

## Heures Supplémentaires

### Calculer les heures supplémentaires

- **POST** `/api/heures-supplementaires/calculer`
- **Body :**
  ```json
{
    "employe_id": "uuid",
    "date_debut": "2024-01-01",
    "date_fin": "2024-01-31"
  }
```

### Approuver les heures supplémentaires

- **POST** `/api/heures-supplementaires/approuver`
- **Body :**
  ```json
{
    "employe_id": "uuid",
    "date_debut": "2024-01-01",
    "date_fin": "2024-01-31",
    "approuve_par": "RH"
  }
```

---

## Paramètres & Barèmes

### Barèmes IRPP

- **GET** `/api/paie/baremes/{annee}/{type}`
  - `type` = MENSUEL ou ANNUEL

### Paramètres système

- **GET** `/api/paie/parametres/{annee}`

---

## Santé & Utilitaires

### Health check

- **GET** `/health`
- **Réponse :**
  ```json
{
    "status": "OK",
    "timestamp": "2024-01-01T12:00:00.000Z",
    "uptime": 123.45,
    "environment": "development"
  }
```

---

## Exemples de flux complets

### Création d'un employé et génération de bulletin

1. **POST** `/api/entreprises` → Récupérer `entrepriseId`
2. **POST** `/api/employes` (avec `entrepriseId`) → Récupérer `employeId`
3. **POST** `/api/employes/{employeId}/generer-bulletin` (avec période et primes)
4. **GET** `/api/employes/{employeId}/bulletins` → Liste des bulletins

---

## Codes d'erreur courants

- `400` : Erreur de validation
- `404` : Ressource non trouvée
- `409` : Conflit (doublon)
- `500` : Erreur serveur

---

## Swagger & Documentation interactive

- **URL** : [http://localhost:3000/api-docs](http://localhost:3000/api-docs)

---

**Pour toute question, consultez le README ou ouvrez une issue.**

## Paramètres & Barèmes

### Barèmes IRPP

- **GET** `/api/paie/baremes/{annee}/{type}`
  - `type` = MENSUEL ou ANNUEL

### Paramètres système

- **GET** `/api/paie/parametres/{annee}`

---

## Santé & Utilitaires

### Health check

- **GET** `/health`
- **Réponse :**
  ```json
{
    "status": "OK",
    "timestamp": "2024-01-01T12:00:00.000Z",
    "uptime": 123.45,
    "environment": "development"
  }
```

---

## Exemples de flux complets

### Création d'un employé et génération de bulletin

1. **POST** `/api/entreprises` → Récupérer `entrepriseId`
2. **POST** `/api/employes` (avec `entrepriseId`) → Récupérer `employeId`
3. **POST** `/api/employes/{employeId}/generer-bulletin` (avec période et primes)
4. **GET** `/api/employes/{employeId}/bulletins` → Liste des bulletins

---

## Codes d'erreur courants

- `400` : Erreur de validation
- `404` : Ressource non trouvée
- `409` : Conflit (doublon)
- `500` : Erreur serveur

---

## Swagger & Documentation interactive

- **URL** : [http://localhost:3000/api-docs](http://localhost:3000/api-docs)

---

**Pour toute question, consultez le README ou ouvrez une issue.**
# 📚 Documentation Complète de l'API SIRH Gestion de Paie

## Table des matières

- [Authentification](#authentification)
- [Entreprises](#entreprises)
- [Employés](#employés)
- [Paie & Bulletins](#paie--bulletins)
- [Prêts Employés](#prêts-employés)
- [Avances sur Salaire](#avances-sur-salaire)
- [Pointages](#pointages)
- [Absences](#absences)
- [Heures Supplémentaires](#heures-supplémentaires)
- [Paramètres & Barèmes](#paramètres--barèmes)
- [Santé & Utilitaires](#santé--utilitaires)

---

## Authentification

> ⚠️ **Note** : L'API actuelle ne nécessite pas d'authentification par défaut. À intégrer selon vos besoins (JWT, OAuth, etc).

---

## Entreprises

### Liste des entreprises

- **GET** `/api/entreprises`
- **Réponse :**
  ```json
{
    "success": true,
    "data": [
      {
        "id": "uuid",
        "raisonSociale": "LGI Consulting SARL",
        "adresse": "123 Avenue de Test, Tunis",
        "siret": "12345678901234",
        "codeNaf": "6202A"
      }
    ]
  }
```

### Créer une entreprise

- **POST** `/api/entreprises`
- **Body :**
  ```json
{
    "raisonSociale": "LGI Consulting SARL",
    "adresse": "123 Avenue de Test, Tunis",
    "siret": "12345678901234",
    "codeNaf": "6202A"
  }
```
- **Réponse :** `201 Created`

### Détail d'une entreprise

- **GET** `/api/entreprises/{id}`

### Liste des employés d'une entreprise

- **GET** `/api/entreprises/{id}/employes?actif=true`

### Statistiques de paie d'une entreprise

- **GET** `/api/entreprises/{id}/statistiques?annee=2024&mois=1`

---

## Employés

### Liste des employés

- **GET** `/api/employes?actif=true&entrepriseId=...`

### Créer un employé

- **POST** `/api/employes`
- **Body :**
  ```json
{
    "entrepriseId": "uuid",
    "nom": "Dupont",
    "prenom": "Jean",
    "fonction": "Développeur",
    "salaireBase": 1200,
    "dateEmbauche": "2023-01-15",
    "personnesACharge": 2,
    "telephone": "+216 20 123 456",
    "email": "<EMAIL>",
    "typeContrat": "CDI",
    "departement": "IT",
    "situationFamiliale": "MARIE"
  }
```

### Détail d'un employé

- **GET** `/api/employes/{id}`

### Modifier un employé

- **PUT** `/api/employes/{id}`

### Liste des bulletins d'un employé

- **GET** `/api/employes/{id}/bulletins?periode=2024-01`

### Générer un bulletin pour un employé

- **POST** `/api/employes/{id}/generer-bulletin`
- **Body :**
  ```json
{
    "periode": "2024-01",
    "sursalaire": 100,
    "anciennete": 50,
    "logementTransport": 100,
    "autres": 200,
    "autresRetenues": 0
  }
```

---

## Paie & Bulletins

### Calculer une paie (simulation)

- **POST** `/api/paie/calculer`
- **Body :**
  ```json
{
    "composants": {
      "salaireBase": 120000,
      "sursalaire": 0,
      "anciennete": 0,
      "logementTransport": 0,
      "autres": 0
    },
    "personnesACharge": 2,
    "periode": "2023-08",
    "autresRetenues": 0
  }
```
- **Réponse :**
  ```json
{
    "success": true,
    "data": {
      "revenuBrut": 120000,
      "cnss": 4800,
      "soldeApresCnss": 115200,
      "abattementProfessionnel": 32256,
      "soldeApresAbattement": 82944,
      "deductionsPersonnesCharge": 0,
      "baseImposable": 82944,
      "baseImposableArrondie": 82944,
      "irpp": 0,
      "totalRetenues": 4800,
      "netAPayer": 115200
    }
  }
```

### Générer et sauvegarder un bulletin

- **POST** `/api/paie/bulletins`
- **Body :** (voir ci-dessus)
- **Réponse :** `201 Created`

### Détail d'un bulletin

- **GET** `/api/paie/bulletins/{id}`

### Liste des bulletins (filtres & pagination)

- **GET** `/api/paie/bulletins?employeId=...&periode=2024-01&statut=VALIDE&page=1&limit=10`

### Modifier le statut d'un bulletin

- **PATCH** `/api/paie/bulletins/{id}/statut`
- **Body :** `{ "statut": "VALIDE" }`

### Exporter un bulletin en PDF

- **GET** `/api/paie/bulletins/{id}/pdf`
- **Réponse :** Fichier PDF

---

## Prêts Employés

### Liste des prêts

- **GET** `/api/prets?employeId=...`

### Détail d'un prêt

- **GET** `/api/prets/{id}`

### Échéances d'un prêt

- **GET** `/api/prets/{id}/echeances`

### Simulation de prêt

- **POST** `/api/prets/simuler`
- **Body :**
  ```json
{
    "montantInitial": 10000,
    "tauxInteret": 0.05,
    "dureeRemboursement": 24
  }
```

### Création d'un prêt

- **POST** `/api/prets`
- **Body :**
  ```json
{
    "employeId": "uuid",
    "montantInitial": 15000,
    "dureeRemboursement": 24,
    "tauxInteret": 0.05,
    "motif": "Achat voiture",
    "garantie": "Salaire"
  }
```

### Traitement d'un remboursement

- **POST** `/api/prets/{id}/remboursement`
- **Body :** `{ "montant": 500 }`

### Modifier le statut d'un prêt

- **PUT** `/api/prets/{id}/statut`
- **Body :** `{ "statut": "SUSPENDU" }`

---

## Avances sur Salaire

### Liste des avances

- **GET** `/api/avances?employeId=...`

### Détail d'une avance

- **GET** `/api/avances/{id}`

### Statut détaillé d'une avance

- **GET** `/api/avances/{id}/statut`

### Plan de remboursement

- **GET** `/api/avances/{id}/plan-remboursement`

### Demander une avance

- **POST** `/api/avances`
- **Body :**
  ```json
{
    "employeId": "uuid",
    "montant": 500,
    "motif": "Urgence familiale",
    "modeRemboursement": "UNIQUE",
    "nombreEcheances": 1
  }
```

### Approbation d'une avance

- **POST** `/api/avances/{id}/approuver`

### Traitement d'un remboursement

- **POST** `/api/avances/{id}/remboursement`
- **Body :** `{ "montant": 200 }`

### Annulation d'une avance

- **DELETE** `/api/avances/{id}`

---

## Pointages

### Liste des pointages

- **GET** `/api/pointages?employeId=...&dateDebut=2024-01-01&dateFin=2024-01-31`

### Ajouter un pointage

- **POST** `/api/pointages`
- **Body :**
  ```json
{
    "employeId": "uuid",
    "date": "2024-01-15",
    "heureArrivee": "08:30",
    "heureDepart": "17:00"
  }
```

---

## Absences

### Liste des absences

- **GET** `/api/absences?employeId=...&dateDebut=2024-01-01&dateFin=2024-01-31`

### Ajouter une absence

- **POST** `/api/absences`
- **Body :**
  ```json
{
    "employe_id": "uuid",
    "date_debut": "2024-01-10",
    "date_fin": "2024-01-12",
    "type_absence": "CONGE_PAYE",
    "motif": "Vacances",
    "impact_paie": false
  }
```

### Approbation d'une absence

- **POST** `/api/absences/{id}/approuver`

---

## Heures Supplémentaires

### Calculer les heures supplémentaires

- **POST** `/api/heures-supplementaires/calculer`
- **Body :**
  ```json
{
    "employe_id": "uuid",
    "date_debut": "2024-01-01",
    "date_fin": "2024-01-31"
  }
```

### Approuver les heures supplémentaires

- **POST** `/api/heures-supplementaires/approuver`
- **Body :**
  ```json
{
    "employe_id": "uuid",
    "date_debut": "2024-01-01",
    "date_fin": "2024-01-31",
    "approuve_par": "RH"
  }
```

---

## Paramètres & Barèmes

### Barèmes IRPP

- **GET** `/api/paie/baremes/{annee}/{type}`
  - `type` = MENSUEL ou ANNUEL

### Paramètres système

- **GET** `/api/paie/parametres/{annee}`

---

## Santé & Utilitaires

### Health check

- **GET** `/health`
- **Réponse :**
  ```json
{
    "status": "OK",
    "timestamp": "2024-01-01T12:00:00.000Z",
    "uptime": 123.45,
    "environment": "development"
  }
```

---

## Exemples de flux complets

### Création d'un employé et génération de bulletin

1. **POST** `/api/entreprises` → Récupérer `entrepriseId`
2. **POST** `/api/employes` (avec `entrepriseId`) → Récupérer `employeId`
3. **POST** `/api/employes/{employeId}/generer-bulletin` (avec période et primes)
4. **GET** `/api/employes/{employeId}/bulletins` → Liste des bulletins

---

## Codes d'erreur courants

- `400` : Erreur de validation
- `404` : Ressource non trouvée
- `409` : Conflit (doublon)
- `500` : Erreur serveur

---

## Swagger & Documentation interactive

- **URL** : [http://localhost:3000/api-docs](http://localhost:3000/api-docs)

---

**Pour toute question, consultez le README ou ouvrez une issue.**
# 📚 Documentation Complète de l'API SIRH Gestion de Paie

## Table des matières

- [Authentification](#authentification)
- [Entreprises](#entreprises)
- [Employés](#employés)
- [Paie & Bulletins](#paie--bulletins)
- [Prêts Employés](#prêts-employés)
- [Avances sur Salaire](#avances-sur-salaire)
- [Pointages](#pointages)
- [Absences](#absences)
- [Heures Supplémentaires](#heures-supplémentaires)
- [Paramètres & Barèmes](#paramètres--barèmes)
- [Santé & Utilitaires](#santé--utilitaires)

---

## Authentification

> ⚠️ **Note** : L'API actuelle ne nécessite pas d'authentification par défaut. À intégrer selon vos besoins (JWT, OAuth, etc).

---

## Entreprises

### Liste des entreprises

- **GET** `/api/entreprises`
- **Réponse :**
  ```json
{
    "success": true,
    "data": [
      {
        "id": "uuid",
        "raisonSociale": "LGI Consulting SARL",
        "adresse": "123 Avenue de Test, Tunis",
        "siret": "12345678901234",
        "codeNaf": "6202A"
      }
    ]
  }
```

### Créer une entreprise

- **POST** `/api/entreprises`
- **Body :**
  ```json
{
    "raisonSociale": "LGI Consulting SARL",
    "adresse": "123 Avenue de Test, Tunis",
    "siret": "12345678901234",
    "codeNaf": "6202A"
  }
```
- **Réponse :** `201 Created`

### Détail d'une entreprise

- **GET** `/api/entreprises/{id}`

### Liste des employés d'une entreprise

- **GET** `/api/entreprises/{id}/employes?actif=true`

### Statistiques de paie d'une entreprise

- **GET** `/api/entreprises/{id}/statistiques?annee=2024&mois=1`

---

## Employés

### Liste des employés

- **GET** `/api/employes?actif=true&entrepriseId=...`

### Créer un employé

- **POST** `/api/employes`
- **Body :**
  ```json
{
    "entrepriseId": "uuid",
    "nom": "Dupont",
    "prenom": "Jean",
    "fonction": "Développeur",
    "salaireBase": 1200,
    "dateEmbauche": "2023-01-15",
    "personnesACharge": 2,
    "telephone": "+216 20 123 456",
    "email": "<EMAIL>",
    "typeContrat": "CDI",
    "departement": "IT",
    "situationFamiliale": "MARIE"
  }
```

### Détail d'un employé

- **GET** `/api/employes/{id}`

### Modifier un employé

- **PUT** `/api/employes/{id}`

### Liste des bulletins d'un employé

- **GET** `/api/employes/{id}/bulletins?periode=2024-01`

### Générer un bulletin pour un employé

- **POST** `/api/employes/{id}/generer-bulletin`
- **Body :**
  ```json
{
    "periode": "2024-01",
    "sursalaire": 100,
    "anciennete": 50,
    "logementTransport": 100,
    "autres": 200,
    "autresRetenues": 0
  }
```

---

## Paie & Bulletins

### Calculer une paie (simulation)

- **POST** `/api/paie/calculer`
- **Body :**
  ```json
{
    "composants": {
      "salaireBase": 120000,
      "sursalaire": 0,
      "anciennete": 0,
      "logementTransport": 0,
      "autres": 0
    },
    "personnesACharge": 2,
    "periode": "2023-08",
    "autresRetenues": 0
  }
```
- **Réponse :**
  ```json
{
    "success": true,
    "data": {
      "revenuBrut": 120000,
      "cnss": 4800,
      "soldeApresCnss": 115200,
      "abattementProfessionnel": 32256,
      "soldeApresAbattement": 82944,
      "deductionsPersonnesCharge": 0,
      "baseImposable": 82944,
      "baseImposableArrondie": 82944,
      "irpp": 0,
      "totalRetenues": 4800,
      "netAPayer": 115200
    }
  }
```

### Générer et sauvegarder un bulletin

- **POST** `/api/paie/bulletins`
- **Body :** (voir ci-dessus)
- **Réponse :** `201 Created`

### Détail d'un bulletin

- **GET** `/api/paie/bulletins/{id}`

### Liste des bulletins (filtres & pagination)

- **GET** `/api/paie/bulletins?employeId=...&periode=2024-01&statut=VALIDE&page=1&limit=10`

### Modifier le statut d'un bulletin

- **PATCH** `/api/paie/bulletins/{id}/statut`
- **Body :** `{ "statut": "VALIDE" }`

### Exporter un bulletin en PDF

- **GET** `/api/paie/bulletins/{id}/pdf`
- **Réponse :** Fichier PDF

---

## Prêts Employés

### Liste des prêts

- **GET** `/api/prets?employeId=...`

### Détail d'un prêt

- **GET** `/api/prets/{id}`

### Échéances d'un prêt

- **GET** `/api/prets/{id}/echeances`

### Simulation de prêt

- **POST** `/api/prets/simuler`
- **Body :**
  ```json
{
    "montantInitial": 10000,
    "tauxInteret": 0.05,
    "dureeRemboursement": 24
  }
```

### Création d'un prêt

- **POST** `/api/prets`
- **Body :**
  ```json
{
    "employeId": "uuid",
    "montantInitial": 15000,
    "dureeRemboursement": 24,
    "tauxInteret": 0.05,
    "motif": "Achat voiture",
    "garantie": "Salaire"
  }
```

### Traitement d'un remboursement

- **POST** `/api/prets/{id}/remboursement`
- **Body :** `{ "montant": 500 }`

### Modifier le statut d'un prêt

- **PUT** `/api/prets/{id}/statut`
- **Body :** `{ "statut": "SUSPENDU" }`

---

## Avances sur Salaire

### Liste des avances

- **GET** `/api/avances?employeId=...`

### Détail d'une avance

- **GET** `/api/avances/{id}`

### Statut détaillé d'une avance

- **GET** `/api/avances/{id}/statut`

### Plan de remboursement

- **GET** `/api/avances/{id}/plan-remboursement`

### Demander une avance

- **POST** `/api/avances`
- **Body :**
  ```json
{
    "employeId": "uuid",
    "montant": 500,
    "motif": "Urgence familiale",
    "modeRemboursement": "UNIQUE",
    "nombreEcheances": 1
  }
```

### Approbation d'une avance

- **POST** `/api/avances/{id}/approuver`

### Traitement d'un remboursement

- **POST** `/api/avances/{id}/remboursement`
- **Body :** `{ "montant": 200 }`

### Annulation d'une avance

- **DELETE** `/api/avances/{id}`

---

## Pointages

### Liste des pointages

- **GET** `/api/pointages?employeId=...&dateDebut=2024-01-01&dateFin=2024-01-31`

### Ajouter un pointage

- **POST** `/api/pointages`
- **Body :**
  ```json
{
    "employeId": "uuid",
    "date": "2024-01-15",
    "heureArrivee": "08:30",
    "heureDepart": "17:00"
  }
```

---

## Absences

### Liste des absences

- **GET** `/api/absences?employeId=...&dateDebut=2024-01-01&dateFin=2024-01-31`

### Ajouter une absence

- **POST** `/api/absences`
- **Body :**
  ```json
{
    "employe_id": "uuid",
    "date_debut": "2024-01-10",
    "date_fin": "2024-01-12",
    "type_absence": "CONGE_PAYE",
    "motif": "Vacances",
    "impact_paie": false
  }
```

### Approbation d'une absence

- **POST** `/api/absences/{id}/approuver`

---

## Heures Supplémentaires

### Calculer les heures supplémentaires

- **POST** `/api/heures-supplementaires/calculer`
- **Body :**
  ```json
{
    "employe_id": "uuid",
    "date_debut": "2024-01-01",
    "date_fin": "2024-01-31"
  }
```

### Approuver les heures supplémentaires

- **POST** `/api/heures-supplementaires/approuver`
- **Body :**
  ```json
{
    "employe_id": "uuid",
    "date_debut": "2024-01-01",
    "date_fin": "2024-01-31",
    "approuve_par": "RH"
  }
```

---

## Paramètres & Barèmes

### Barèmes IRPP

- **GET** `/api/paie/baremes/{annee}/{type}`
  - `type` = MENSUEL ou ANNUEL

### Paramètres système

- **GET** `/api/paie/parametres/{annee}`

---

## Santé & Utilitaires

### Health check

- **GET** `/health`
- **Réponse :**
  ```json
  {
    "status": "OK",
    "timestamp": "2024-01-01T12:00:00.000Z",
    "uptime": 123.45,
    "environment": "development"
  }
```

---

## Exemples de flux complets

### Création d'un employé et génération de bulletin

1. **POST** `/api/entreprises` → Récupérer `entrepriseId`
2. **POST** `/api/employes` (avec `entrepriseId`) → Récupérer `employeId`
3. **POST** `/api/employes/{employeId}/generer-bulletin` (avec période et primes)
4. **GET** `/api/employes/{employeId}/bulletins` → Liste des bulletins

---

## Codes d'erreur courants

- `400` : Erreur de validation
- `404` : Ressource non trouvée
- `409` : Conflit (doublon)
- `500` : Erreur serveur

---

## Swagger & Documentation interactive

- **URL** : [http://localhost:3000/api-docs](http://localhost:3000/api-docs)

---

**Pour toute question, consultez le README ou ouvrez une issue.**

## Paramètres & Barèmes

### Barèmes IRPP

- **GET** `/api/paie/baremes/{annee}/{type}`
  - `type` = MENSUEL ou ANNUEL

### Paramètres système

- **GET** `/api/paie/parametres/{annee}`

---

## Santé & Utilitaires

### Health check

- **GET** `/health`
- **Réponse :**
  ```json
{
    "status": "OK",
    "timestamp": "2024-01-01T12:00:00.000Z",
    "uptime": 123.45,
    "environment": "development"
  }
```

---

## Exemples de flux complets

### Création d'un employé et génération de bulletin

1. **POST** `/api/entreprises` → Récupérer `entrepriseId`
2. **POST** `/api/employes` (avec `entrepriseId`) → Récupérer `employeId`
3. **POST** `/api/employes/{employeId}/generer-bulletin` (avec période et primes)
4. **GET** `/api/employes/{employeId}/bulletins` → Liste des bulletins

---

## Codes d'erreur courants

- `400` : Erreur de validation
- `404` : Ressource non trouvée
- `409` : Conflit (doublon)
- `500` : Erreur serveur

---

## Swagger & Documentation interactive

- **URL** : [http://localhost:3000/api-docs](http://localhost:3000/api-docs)

---

**Pour toute question, consultez le README ou ouvrez une issue.**
# 📚 Documentation Complète de l'API SIRH Gestion de Paie

## Table des matières

- [Authentification](#authentification)
- [Entreprises](#entreprises)
- [Employés](#employés)
- [Paie & Bulletins](#paie--bulletins)
- [Prêts Employés](#prêts-employés)
- [Avances sur Salaire](#avances-sur-salaire)
- [Pointages](#pointages)
- [Absences](#absences)
- [Heures Supplémentaires](#heures-supplémentaires)
- [Paramètres & Barèmes](#paramètres--barèmes)
- [Santé & Utilitaires](#santé--utilitaires)

---

## Authentification

> ⚠️ **Note** : L'API actuelle ne nécessite pas d'authentification par défaut. À intégrer selon vos besoins (JWT, OAuth, etc).

---

## Entreprises

### Liste des entreprises

- **GET** `/api/entreprises`
- **Réponse :**
  ```json
  {
    "success": true,
    "data": [
      {
        "id": "uuid",
        "raisonSociale": "LGI Consulting SARL",
        "adresse": "123 Avenue de Test, Tunis",
        "siret": "12345678901234",
        "codeNaf": "6202A"
      }
    ]
  }
  ```

### Créer une entreprise

- **POST** `/api/entreprises`
- **Body :**
  ```json
  {
    "raisonSociale": "LGI Consulting SARL",
    "adresse": "123 Avenue de Test, Tunis",
    "siret": "12345678901234",
    "codeNaf": "6202A"
  }
  ```
- **Réponse :** `201 Created`

### Détail d'une entreprise

- **GET** `/api/entreprises/{id}`

### Liste des employés d'une entreprise

- **GET** `/api/entreprises/{id}/employes?actif=true`

### Statistiques de paie d'une entreprise

- **GET** `/api/entreprises/{id}/statistiques?annee=2024&mois=1`

---

## Employés

### Liste des employés

- **GET** `/api/employes?actif=true&entrepriseId=...`

### Créer un employé

- **POST** `/api/employes`
- **Body :**
  ```json
  {
    "entrepriseId": "uuid",
    "nom": "Dupont",
    "prenom": "Jean",
    "fonction": "Développeur",
    "salaireBase": 1200,
    "dateEmbauche": "2023-01-15",
    "personnesACharge": 2,
    "telephone": "+216 20 123 456",
    "email": "<EMAIL>",
    "typeContrat": "CDI",
    "departement": "IT",
    "situationFamiliale": "MARIE"
  }
  ```

### Détail d'un employé

- **GET** `/api/employes/{id}`

### Modifier un employé

- **PUT** `/api/employes/{id}`

### Liste des bulletins d'un employé

- **GET** `/api/employes/{id}/bulletins?periode=2024-01`

### Générer un bulletin pour un employé

- **POST** `/api/employes/{id}/generer-bulletin`
- **Body :**
  ```json
  {
    "periode": "2024-01",
    "sursalaire": 100,
    "anciennete": 50,
    "logementTransport": 100,
    "autres": 200,
    "autresRetenues": 0
  }
  ```

---

## Paie & Bulletins

### Calculer une paie (simulation)

- **POST** `/api/paie/calculer`
- **Body :**
  ```json
  {
    "composants": {
      "salaireBase": 120000,
      "sursalaire": 0,
      "anciennete": 0,
      "logementTransport": 0,
      "autres": 0
    },
    "personnesACharge": 2,
    "periode": "2023-08",
    "autresRetenues": 0
  }
  ```
- **Réponse :**
  ```json
  {
    "success": true,
    "data": {
      "revenuBrut": 120000,
      "cnss": 4800,
      "soldeApresCnss": 115200,
      "abattementProfessionnel": 32256,
      "soldeApresAbattement": 82944,
      "deductionsPersonnesCharge": 0,
      "baseImposable": 82944,
      "baseImposableArrondie": 82944,
      "irpp": 0,
      "totalRetenues": 4800,
      "netAPayer": 115200
    }
  }
  ```

### Générer et sauvegarder un bulletin

- **POST** `/api/paie/bulletins`
- **Body :** (voir ci-dessus)
- **Réponse :** `201 Created`

### Détail d'un bulletin

- **GET** `/api/paie/bulletins/{id}`

### Liste des bulletins (filtres & pagination)

- **GET** `/api/paie/bulletins?employeId=...&periode=2024-01&statut=VALIDE&page=1&limit=10`

### Modifier le statut d'un bulletin

- **PATCH** `/api/paie/bulletins/{id}/statut`
- **Body :** `{ "statut": "VALIDE" }`

### Exporter un bulletin en PDF

- **GET** `/api/paie/bulletins/{id}/pdf`
- **Réponse :** Fichier PDF

---

## Prêts Employés

### Liste des prêts

- **GET** `/api/prets?employeId=...`

### Détail d'un prêt

- **GET** `/api/prets/{id}`

### Échéances d'un prêt

- **GET** `/api/prets/{id}/echeances`

### Simulation de prêt

- **POST** `/api/prets/simuler`
- **Body :**
  ```json
  {
    "montantInitial": 10000,
    "tauxInteret": 0.05,
    "dureeRemboursement": 24
  }
  ```

### Création d'un prêt

- **POST** `/api/prets`
- **Body :**
  ```json
  {
    "employeId": "uuid",
    "montantInitial": 15000,
    "dureeRemboursement": 24,
    "tauxInteret": 0.05,
    "motif": "Achat voiture",
    "garantie": "Salaire"
  }
  ```

### Traitement d'un remboursement

- **POST** `/api/prets/{id}/remboursement`
- **Body :** `{ "montant": 500 }`

### Modifier le statut d'un prêt

- **PUT** `/api/prets/{id}/statut`
- **Body :** `{ "statut": "SUSPENDU" }`

---

## Avances sur Salaire

### Liste des avances

- **GET** `/api/avances?employeId=...`

### Détail d'une avance

- **GET** `/api/avances/{id}`

### Statut détaillé d'une avance

- **GET** `/api/avances/{id}/statut`

### Plan de remboursement

- **GET** `/api/avances/{id}/plan-remboursement`

### Demander une avance

- **POST** `/api/avances`
- **Body :**
  ```json
  {
    "employeId": "uuid",
    "montant": 500,
    "motif": "Urgence familiale",
    "modeRemboursement": "UNIQUE",
    "nombreEcheances": 1
  }
  ```

### Approbation d'une avance

- **POST** `/api/avances/{id}/approuver`

### Traitement d'un remboursement

- **POST** `/api/avances/{id}/remboursement`
- **Body :** `{ "montant": 200 }`

### Annulation d'une avance

- **DELETE** `/api/avances/{id}`

---

## Pointages

### Liste des pointages

- **GET** `/api/pointages?employeId=...&dateDebut=2024-01-01&dateFin=2024-01-31`

### Ajouter un pointage

- **POST** `/api/pointages`
- **Body :**
  ```json
  {
    "employeId": "uuid",
    "date": "2024-01-15",
    "heureArrivee": "08:30",
    "heureDepart": "17:00"
  }
  ```

---

## Absences

### Liste des absences

- **GET** `/api/absences?employeId=...&dateDebut=2024-01-01&dateFin=2024-01-31`

### Ajouter une absence

- **POST** `/api/absences`
- **Body :**
  ```json
  {
    "employe_id": "uuid",
    "date_debut": "2024-01-10",
    "date_fin": "2024-01-12",
    "type_absence": "CONGE_PAYE",
    "motif": "Vacances",
    "impact_paie": false
  }
  ```

### Approbation d'une absence

- **POST** `/api/absences/{id}/approuver`

---

## Heures Supplémentaires

### Calculer les heures supplémentaires

- **POST** `/api/heures-supplementaires/calculer`
- **Body :**
  ```json
  {
    "employe_id": "uuid",
    "date_debut": "2024-01-01",
    "date_fin": "2024-01-31"
  }
  ```

### Approuver les heures supplémentaires

- **POST** `/api/heures-supplementaires/approuver`
- **Body :**
  ```json
  {
    "employe_id": "uuid",
    "date_debut": "2024-01-01",
    "date_fin": "2024-01-31",
    "approuve_par": "RH"
  }
  ```

---

## Paramètres & Barèmes

### Barèmes IRPP

- **GET** `/api/paie/baremes/{annee}/{type}`
  - `type` = MENSUEL ou ANNUEL

### Paramètres système

- **GET** `/api/paie/parametres/{annee}`

---

## Santé & Utilitaires

### Health check

- **GET** `/health`
- **Réponse :**
  ```json
  {
    "status": "OK",
    "timestamp": "2024-01-01T12:00:00.000Z",
    "uptime": 123.45,
    "environment": "development"
  }
  ```

---

## Exemples de flux complets

### Création d'un employé et génération de bulletin

1. **POST** `/api/entreprises` → Récupérer `entrepriseId`
2. **POST** `/api/employes` (avec `entrepriseId`) → Récupérer `employeId`
3. **POST** `/api/employes/{employeId}/generer-bulletin` (avec période et primes)
4. **GET** `/api/employes/{employeId}/bulletins` → Liste des bulletins

---

## Codes d'erreur courants

- `400` : Erreur de validation
- `404` : Ressource non trouvée
- `409` : Conflit (doublon)
- `500` : Erreur serveur

---

## Swagger & Documentation interactive

- **URL** : [http://localhost:3000/api-docs](http://localhost:3000/api-docs)

---

**Pour toute question, consultez le README ou ouvrez une issue.**
