import os
from pathlib import Path

import os
os.environ.setdefault('LC_ALL', 'C.UTF-8')
os.environ.setdefault('LANG', 'C.UTF-8')

# Base settings
BASE_DIR = Path(__file__).resolve().parent.parent.parent

SECRET_KEY = os.getenv('SECRET_KEY', 'your_default_secret_key')

DEBUG = False
ALLOWED_HOSTS = ['*']

# STATIC_ROOT = os.path.join(BASE_DIR, 'static/')

INSTALLED_APPS = [
    'unfold',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.humanize',
    'widget_tweaks',
    
    'djmoney',
    
    'apps.accounts',
    'apps.common',
    'apps.accounting',
    'apps.cash',
    'apps.clients',
    'apps.suppliers',
    'apps.core',
    'apps.purchases',
    'apps.reports',
    'apps.sales',
    'apps.stocks',
    'apps.deposits',
    'apps.sirh',
    'apps.prets',
    'apps.pointage',
    'apps.absence',
    'apps.hours',
    'django_htmx',
]

UNFOLD = { 
    "SITE_TITLE": "Lotus V5",
    "SITE_HEADER": "Lotus V5",
    "SITE_URL": "/",
    "THEME_SWITCHER": True,
    "SHOW_HISTORY": True,
}


CURRENCIES = ('XOF',)

DJANGO_MONEY = {
    'DEFAULT_CURRENCY': 'XOF',
    'CURRENCY_DECIMAL_PLACES': 0,  # No decimals for CFA
    'CURRENCY_DECIMAL_SEPARATOR': ',',
    'CURRENCY_THOUSAND_SEPARATOR': ' ',
    
    # Format settings
    'CURRENCY_FORMATS': {
        'XOF': {
            'format': '%n FCFA',  # FCFA before number
            'decimal_digits': 0,
            'force_grouping': True,
        },
    },
}

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    
    "django_htmx.middleware.HtmxMiddleware",

    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    
    # Middleware personnalisé pour la sécurité et les permissions
    'apps.accounts.middleware.SecurityMiddleware',
    'apps.accounts.middleware.PermissionMiddleware',
    'apps.accounts.middleware.AutoLogoutMiddleware',
    'apps.accounts.middleware.LogoutTrackingMiddleware',
    
    # 'django.middleware.clickjacking.XContentOptionsMiddleware',
]

ROOT_URLCONF = 'config.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': ['templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'apps.stocks.context_processors.sidebar_context',
                'apps.stocks.context_processors.notifications_context',
                'apps.stocks.context_processors.clients_context',
                'apps.cash.context_processors.caisse_context',
                'apps.sales.context_processors.today_stats_context',
            ],
        },
    },
]

WSGI_APPLICATION = 'config.wsgi.application'
ASGI_APPLICATION = 'config.asgi.application'


LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'UTC'

USE_I18N = True
USE_L10N = True
USE_TZ = True


STATIC_URL = '/static/'
MEDIA_URL = '/media/'

MEDIA_ROOT = os.path.join(BASE_DIR, 'media')
STATICFILES_DIRS = [os.path.join(BASE_DIR, 'static')]

STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')

# Configuration pour servir les fichiers statiques
STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
]


# if not DEBUG:
#     STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'


AUTH_USER_MODEL = 'accounts.User'
LOGIN_URL = '/accounts/connexion/'  # Redirection vers cette URL si on tente d'accéder sans être connecté
LOGIN_REDIRECT_URL = '/'  # Redirection après connexion réussie
LOGOUT_URL = '/accounts/deconnexion/'  # URL de déconnexion
LOGOUT_REDIRECT_URL = '/accounts/connexion/'  # Redirection après déconnexion

# Configuration des sessions pour la sécurité
SESSION_COOKIE_AGE = 3600 * 8  # 8 heures
SESSION_EXPIRE_AT_BROWSER_CLOSE = True
SESSION_SAVE_EVERY_REQUEST = True

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Configuration du cache
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',
        'TIMEOUT': 300,  # 5 minutes par défaut
        'OPTIONS': {
            'MAX_ENTRIES': 1000,
            'CULL_FREQUENCY': 3,
        }
    }
}
