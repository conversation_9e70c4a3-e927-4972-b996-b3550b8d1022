from django.contrib import admin
from django.urls import path
from django.conf import settings
from django.urls import include, path
from django.conf.urls.static import static
from django.contrib.auth.decorators import login_required

urlpatterns = [
    path("admin/", admin.site.urls),
    path("hours/", include("apps.hours.urls")),
    path("absence/", include("apps.absence.urls")),
    path("pointage/", include("apps.pointage.urls")),
    path("prets/", include("apps.prets.urls")),
    path("sirh/", include("apps.sirh.urls")),
    path("absences/", include("apps.absence.urls")),
    path("accounts/", include("apps.accounts.urls")),
    path("caisse/", include("apps.cash.urls")),
    path("stock/", include("apps.stocks.urls")),
    path("sales/", include("apps.sales.urls")),
    path('purchases/', include("apps.purchases.urls")),
    path('client/', include("apps.clients.urls")),
    path('supplier/', include("apps.suppliers.urls")),
    path('deposits/', include("apps.deposits.urls")),
    path("reports/", include("apps.reports.urls")),
    path("",  include("apps.core.urls", namespace="core"))
] 
if settings.DEBUG:
    # En mode DEBUG, servir les fichiers statiques depuis STATICFILES_DIRS
    from django.contrib.staticfiles.views import serve
    from django.urls import re_path
    urlpatterns += [
        re_path(r'^static/(?P<path>.*)$', serve),
    ]
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

if settings.DEBUG:
    import debug_toolbar
    urlpatterns += [
        path('__debug__/', include(debug_toolbar.urls)),
    ]
