// static/sirh/js/employes_edit.js
// Gestion AJAX de la modification d'un employé SIRH

document.addEventListener('DOMContentLoaded', function () {
    const form = document.getElementById('employe-edit-form');
    const errorDiv = document.getElementById('employe-edit-error');
    const successDiv = document.getElementById('employe-edit-success');

    // Récupère l'id employé depuis l'URL
    const match = window.location.pathname.match(/employes\/(.+)\/modifier/);
    const employeId = match ? match[1] : null;
    if (!employeId) {
        errorDiv.textContent = "Impossible de déterminer l'employé à modifier.";
        errorDiv.style.display = 'block';
        form.style.display = 'none';
        return;
    }

    // Pré-remplir le formulaire avec les données actuelles
    fetch(`/sirh/api/employes/${employeId}/`)
        .then(r => r.json())
        .then(data => {
            let emp = data.data || data;
            form.nom.value = emp.nom || '';
            form.prenom.value = emp.prenom || '';
            form.fonction.value = emp.fonction || '';
            form.salaireBase.value = emp.salaireBase || '';
            form.dateEmbauche.value = emp.dateEmbauche ? emp.dateEmbauche.substring(0,10) : '';
            form.personnesACharge.value = emp.personnesACharge || 0;
            form.email.value = emp.email || '';
            form.telephone.value = emp.telephone || '';
            form.typeContrat.value = emp.typeContrat || 'CDI';
            form.departement.value = emp.departement || '';
            form.situationFamiliale.value = emp.situationFamiliale || 'CELIBATAIRE';
        })
        .catch(e => {
            errorDiv.textContent = "Erreur lors du chargement des données : " + e.message;
            errorDiv.style.display = 'block';
            form.style.display = 'none';
        });

    form.addEventListener('submit', function (e) {
        e.preventDefault();
        errorDiv.style.display = 'none';
        successDiv.style.display = 'none';
        const formData = new FormData(form);
        const payload = {};
        for (let [key, value] of formData.entries()) {
            if (value !== null && value !== '') payload[key] = value;
        }
        fetch(`/sirh/api/employes/${employeId}/`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload),
        })
            .then(async r => {
                let json = null;
                try { json = await r.clone().json(); } catch(e) {}
                if (!r.ok) {
                    let msg = `Erreur API: ${r.status} ${r.statusText}`;
                    if (json && json.error) msg += `\n${json.error}`;
                    throw new Error(msg);
                }
                return json;
            })
            .then(data => {
                successDiv.textContent = 'Employé modifié avec succès !';
                successDiv.style.display = 'block';
            })
            .catch(e => {
                errorDiv.textContent = e.message;
                errorDiv.style.display = 'block';
            });
    });
});
