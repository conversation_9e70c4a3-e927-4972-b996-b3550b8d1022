// static/sirh/js/employes_add.js
// Gestion AJAX de l'ajout d'un employé SIRH

document.addEventListener('DOMContentLoaded', function () {
    const form = document.getElementById('employe-add-form');
    const errorDiv = document.getElementById('employe-add-error');
    const successDiv = document.getElementById('employe-add-success');

    // Récupère l'entrepriseId par défaut (à adapter si multi-entreprise)
    let entrepriseId = null;
    fetch('/sirh/api/entreprises/')
        .then(r => r.json())
        .then(data => {
            if (data.data && data.data.length > 0) {
                entrepriseId = data.data[0].id;
            }
        });

    form.addEventListener('submit', function (e) {
        e.preventDefault();
        errorDiv.style.display = 'none';
        successDiv.style.display = 'none';
        if (!entrepriseId) {
            errorDiv.textContent = "Impossible de déterminer l'entreprise.";
            errorDiv.style.display = 'block';
            return;
        }
        const formData = new FormData(form);
        const payload = {
            entrepriseId: entrepriseId,
            nom: formData.get('nom'),
            prenom: formData.get('prenom'),
            fonction: formData.get('fonction'),
            salaireBase: Number(formData.get('salaireBase')),
            dateEmbauche: formData.get('dateEmbauche'),
            personnesACharge: Number(formData.get('personnesACharge')),
            email: formData.get('email'),
            telephone: formData.get('telephone'),
            typeContrat: formData.get('typeContrat'),
            departement: formData.get('departement'),
            situationFamiliale: formData.get('situationFamiliale'),
        };
        fetch('/sirh/api/employes/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload),
        })
            .then(async r => {
                let json = null;
                try { json = await r.clone().json(); } catch(e) {}
                if (!r.ok) {
                    let msg = `Erreur API: ${r.status} ${r.statusText}`;
                    if (json && json.error) msg += `\n${json.error}`;
                    throw new Error(msg);
                }
                return json;
            })
            .then(data => {
                successDiv.textContent = 'Employé ajouté avec succès !';
                successDiv.style.display = 'block';
                form.reset();
            })
            .catch(e => {
                errorDiv.textContent = e.message;
                errorDiv.style.display = 'block';
            });
    });
});
