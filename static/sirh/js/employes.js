// static/sirh/js/employes.js
// Gestion AJAX de la liste des employés SIRH

document.addEventListener('DOMContentLoaded', function () {
    const tableBody = document.querySelector('#employes-table tbody');
    const loader = document.querySelector('#employes-loader');
    const errorDiv = document.querySelector('#employes-error');

    function fetchEmployes() {
        loader.style.display = 'block';
        errorDiv.style.display = 'none';
        fetch('/sirh/api/employes/')
            .then(response => {
                if (!response.ok) throw new Error('Erreur lors du chargement');
                return response.json();
            })
            .then(data => {
              tableBody.innerHTML = "";
              // Si la réponse contient un tableau d'employés dans data.data
              if (Array.isArray(data.data)) {
                data.data.forEach((emp) => {
                  const tr = document.createElement("tr");
                  tr.innerHTML = `
              <td>${emp.id}</td>
              <td>${emp.nom}</td>
              <td>${emp.prenom}</td>
              <td>${emp.email || ""}</td>
              <td>${emp.poste || emp.fonction || ""}</td>
              <td class="actions">
                <a href="/sirh/employes/${
                  emp.id
                }/" class="btn btn-sm btn-outline-primary me-1">Voir</a>
                <a href="/sirh/employes/${
                  emp.id
                }/modifier/" class="btn btn-sm btn-outline-warning me-1">Modifier</a>
                <a href="/sirh/employes/${
                  emp.id
                }/bulletins/" class="btn btn-sm btn-info me-1">Bulletins</a>
                <button class="btn btn-sm btn-primary btn-pdf-bulletin" data-employe-id="${
                  emp.id
                }" title="Dernier bulletin PDF"><i class="bi bi-file-earmark-pdf"></i> PDF</button>
              </td>
            `;
                  tableBody.appendChild(tr);
                });
              } else if (data.error) {
                errorDiv.textContent =
                  data.error + (data.traceback ? "\n" + data.traceback : "");
                errorDiv.style.display = "block";
              } else {
                errorDiv.textContent = "Aucune donnée à afficher.";
                errorDiv.style.display = "block";
              }
              loader.style.display = "none";

              // Ajout du handler pour les boutons PDF
              document.querySelectorAll(".btn-pdf-bulletin").forEach((btn) => {
                btn.addEventListener("click", async function (e) {
                  e.preventDefault();
                  const employeId = btn.getAttribute("data-employe-id");
                  try {
                    // On récupère le dernier bulletin (par période décroissante)
                    const resp = await fetch(
                      `/sirh/api/employes/${employeId}/bulletins/?limit=1&ordering=-periode`,
                      {
                        credentials: "same-origin",
                        headers: { "X-Requested-With": "XMLHttpRequest" },
                      }
                    );
                    const data = await resp.json();
                    let bulletin = null;
                    if (Array.isArray(data.data)) {
                      bulletin = data.data[0];
                    } else if (Array.isArray(data)) {
                      bulletin = data[0];
                    } else if (data && data.id) {
                      bulletin = data;
                    }
                    if (bulletin && bulletin.id) {
                      window.open(
                        `/sirh/api/paie/bulletins/${bulletin.id}/pdf`,
                        "_blank"
                      );
                    } else {
                      alert("Aucun bulletin trouvé pour cet employé.");
                    }
                  } catch (err) {
                    alert("Erreur lors de la récupération du bulletin.");
                  }
                });
              });
            })
            .catch(err => {
                loader.style.display = 'none';
                errorDiv.textContent = err.message;
                errorDiv.style.display = 'block';
            });
    }

    if (tableBody) {
        fetchEmployes();
    }
});
