"""
Système de cache intelligent pour les absences.
Optimise les performances des appels API avec invalidation sélective.
"""

import hashlib
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union, Callable
from django.core.cache import cache
from django.conf import settings

logger = logging.getLogger(__name__)

class AbsenceCache:
    """
    Gestionnaire de cache intelligent pour les absences.
    Gère la mise en cache, l'invalidation et la cohérence des données.
    """
    
    # Préfixes de clés de cache
    PREFIX_ABSENCES_LIST = "absences_list"
    PREFIX_ABSENCE_DETAIL = "absence_detail"
    PREFIX_EMPLOYES_LIST = "employes_list"
    PREFIX_STATS = "absence_stats"
    
    # Durées de cache par défaut (en secondes)
    DEFAULT_CACHE_TIMEOUT = getattr(settings, 'ABSENCE_CACHE_TIMEOUT', 300)  # 5 minutes
    EMPLOYES_CACHE_TIMEOUT = getattr(settings, 'EMPLOYES_CACHE_TIMEOUT', 900)  # 15 minutes
    STATS_CACHE_TIMEOUT = getattr(settings, 'STATS_CACHE_TIMEOUT', 600)  # 10 minutes
    
    # Taille maximale des listes en cache
    MAX_CACHED_LIST_SIZE = getattr(settings, 'MAX_CACHED_LIST_SIZE', 1000)
    
    def __init__(self):
        self.cache_backend = cache
    
    def get_absences_list(self, params: Dict[str, Any], fetcher: Callable) -> Dict[str, Any]:
        """
        Récupère une liste d'absences depuis le cache ou via le fetcher.
        
        Args:
            params: Paramètres de filtrage
            fetcher: Fonction pour récupérer les données si pas en cache
            
        Returns:
            Dict contenant la liste des absences
        """
        cache_key = self._generate_list_cache_key(params)
        
        # Tentative de récupération depuis le cache
        cached_data = self.cache_backend.get(cache_key)
        if cached_data is not None:
            logger.debug(f"Cache hit pour liste absences: {cache_key}")
            return cached_data
        
        # Récupération des données
        logger.debug(f"Cache miss pour liste absences: {cache_key}")
        data = fetcher(params)
        
        # Vérification de la taille avant mise en cache
        if self._is_cacheable_list(data):
            self.cache_backend.set(cache_key, data, self.DEFAULT_CACHE_TIMEOUT)
            logger.debug(f"Mise en cache de la liste absences: {cache_key}")
        else:
            logger.warning(f"Liste trop volumineuse pour le cache: {len(data.get('data', []))} éléments")
        
        return data
    
    def get_absence_detail(self, absence_id: str, fetcher: Callable) -> Dict[str, Any]:
        """
        Récupère le détail d'une absence depuis le cache ou via le fetcher.
        
        Args:
            absence_id: ID de l'absence
            fetcher: Fonction pour récupérer les données si pas en cache
            
        Returns:
            Dict contenant les détails de l'absence
        """
        cache_key = f"{self.PREFIX_ABSENCE_DETAIL}_{absence_id}"
        
        # Tentative de récupération depuis le cache
        cached_data = self.cache_backend.get(cache_key)
        if cached_data is not None:
            logger.debug(f"Cache hit pour absence {absence_id}")
            return cached_data
        
        # Récupération des données
        logger.debug(f"Cache miss pour absence {absence_id}")
        data = fetcher(absence_id)
        
        # Mise en cache
        self.cache_backend.set(cache_key, data, self.DEFAULT_CACHE_TIMEOUT)
        logger.debug(f"Mise en cache de l'absence {absence_id}")
        
        return data
    
    def get_employes_list(self, fetcher: Callable) -> Dict[str, Any]:
        """
        Récupère la liste des employés depuis le cache ou via le fetcher.
        
        Args:
            fetcher: Fonction pour récupérer les données si pas en cache
            
        Returns:
            Dict contenant la liste des employés
        """
        cache_key = self.PREFIX_EMPLOYES_LIST
        
        # Tentative de récupération depuis le cache
        cached_data = self.cache_backend.get(cache_key)
        if cached_data is not None:
            logger.debug("Cache hit pour liste employés")
            return cached_data
        
        # Récupération des données
        logger.debug("Cache miss pour liste employés")
        data = fetcher()
        
        # Mise en cache avec timeout plus long
        self.cache_backend.set(cache_key, data, self.EMPLOYES_CACHE_TIMEOUT)
        logger.debug("Mise en cache de la liste employés")
        
        return data
    
    def invalidate_absence(self, absence_id: str) -> None:
        """
        Invalide le cache pour une absence spécifique.
        
        Args:
            absence_id: ID de l'absence à invalider
        """
        # Invalider le détail de l'absence
        detail_key = f"{self.PREFIX_ABSENCE_DETAIL}_{absence_id}"
        self.cache_backend.delete(detail_key)
        logger.debug(f"Cache invalidé pour absence {absence_id}")
        
        # Invalider les listes d'absences (approche conservatrice)
        self._invalidate_lists_cache()
    
    def invalidate_employe_absences(self, employe_id: str) -> None:
        """
        Invalide le cache pour toutes les absences d'un employé.
        
        Args:
            employe_id: ID de l'employé
        """
        # Pour l'instant, invalidation globale des listes
        # En production, on pourrait maintenir un index employe_id -> cache_keys
        self._invalidate_lists_cache()
        logger.debug(f"Cache invalidé pour les absences de l'employé {employe_id}")
    
    def invalidate_all_absences(self) -> None:
        """
        Invalide tout le cache des absences.
        """
        self._invalidate_lists_cache()
        self._invalidate_details_cache()
        logger.info("Cache complet des absences invalidé")
    
    def invalidate_employes(self) -> None:
        """
        Invalide le cache de la liste des employés.
        """
        self.cache_backend.delete(self.PREFIX_EMPLOYES_LIST)
        logger.debug("Cache de la liste employés invalidé")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        Retourne des statistiques sur l'utilisation du cache.
        
        Returns:
            Dict avec les statistiques de cache
        """
        stats = {
            'cache_backend': str(type(self.cache_backend)),
            'default_timeout': self.DEFAULT_CACHE_TIMEOUT,
            'employes_timeout': self.EMPLOYES_CACHE_TIMEOUT,
            'max_list_size': self.MAX_CACHED_LIST_SIZE,
            'timestamp': datetime.now().isoformat()
        }
        
        # Tenter de récupérer des métriques si disponibles
        try:
            # Vérifier quelques clés communes
            test_keys = [
                self.PREFIX_EMPLOYES_LIST,
                f"{self.PREFIX_ABSENCE_DETAIL}_test",
                f"{self.PREFIX_ABSENCES_LIST}_test"
            ]
            
            cached_keys = []
            for key in test_keys:
                if self.cache_backend.get(key) is not None:
                    cached_keys.append(key)
            
            stats['sample_cached_keys'] = cached_keys
            
        except Exception as e:
            logger.warning(f"Impossible de récupérer les statistiques de cache: {e}")
            stats['error'] = str(e)
        
        return stats
    
    def warm_up_cache(self, employes_fetcher: Callable, 
                     recent_absences_fetcher: Callable) -> Dict[str, Any]:
        """
        Préchauffe le cache avec les données les plus utilisées.
        
        Args:
            employes_fetcher: Fonction pour récupérer les employés
            recent_absences_fetcher: Fonction pour récupérer les absences récentes
            
        Returns:
            Dict avec le résultat du préchauffage
        """
        results = {
            'employes': False,
            'recent_absences': False,
            'errors': []
        }
        
        try:
            # Précharger la liste des employés
            self.get_employes_list(employes_fetcher)
            results['employes'] = True
            logger.info("Cache des employés préchauffé")
            
        except Exception as e:
            error_msg = f"Erreur lors du préchauffage des employés: {e}"
            logger.error(error_msg)
            results['errors'].append(error_msg)
        
        try:
            # Précharger les absences récentes (30 derniers jours)
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=30)
            
            recent_params = {
                'dateDebut': start_date.strftime('%Y-%m-%d'),
                'dateFin': end_date.strftime('%Y-%m-%d')
            }
            
            self.get_absences_list(recent_params, recent_absences_fetcher)
            results['recent_absences'] = True
            logger.info("Cache des absences récentes préchauffé")
            
        except Exception as e:
            error_msg = f"Erreur lors du préchauffage des absences récentes: {e}"
            logger.error(error_msg)
            results['errors'].append(error_msg)
        
        return results
    
    def _generate_list_cache_key(self, params: Dict[str, Any]) -> str:
        """
        Génère une clé de cache pour une liste d'absences.
        
        Args:
            params: Paramètres de filtrage
            
        Returns:
            Clé de cache unique
        """
        # Normaliser les paramètres pour une clé cohérente
        normalized_params = {k: v for k, v in sorted(params.items()) if v is not None and v != ''}
        
        # Créer un hash des paramètres
        params_str = json.dumps(normalized_params, sort_keys=True)
        params_hash = hashlib.md5(params_str.encode()).hexdigest()[:8]
        
        return f"{self.PREFIX_ABSENCES_LIST}_{params_hash}"
    
    def _is_cacheable_list(self, data: Dict[str, Any]) -> bool:
        """
        Détermine si une liste est cacheable selon sa taille.
        
        Args:
            data: Données à évaluer
            
        Returns:
            True si cacheable, False sinon
        """
        if not isinstance(data, dict):
            return False
        
        items = data.get('data', [])
        if not isinstance(items, list):
            return False
        
        return len(items) <= self.MAX_CACHED_LIST_SIZE
    
    def _invalidate_lists_cache(self) -> None:
        """
        Invalide toutes les listes d'absences en cache.
        
        Note: Django ne fournit pas de méthode native pour supprimer par pattern.
        En production, considérer l'utilisation de Redis avec SCAN/DEL.
        """
        # Pour l'instant, on ne peut pas facilement invalider par pattern avec Django
        # Cette méthode sert de placeholder pour une implémentation future
        logger.debug("Invalidation des listes d'absences (pattern-based non supporté)")
    
    def _invalidate_details_cache(self) -> None:
        """
        Invalide tous les détails d'absences en cache.
        """
        # Même limitation que pour les listes
        logger.debug("Invalidation des détails d'absences (pattern-based non supporté)")

# Instance globale du gestionnaire de cache
default_cache = AbsenceCache()
