"""
Système de validation des données pour les absences.
Valide les dates, types d'absence, employés et autres contraintes métier.
"""

import re
from datetime import datetime, date, timedelta
from typing import Dict, Any, List, Optional, Union
from django.conf import settings
import logging

logger = logging.getLogger(__name__)

class ValidationError(Exception):
    """Exception levée lors d'erreurs de validation"""
    def __init__(self, message: str, field: Optional[str] = None, code: Optional[str] = None):
        super().__init__(message)
        self.field = field
        self.code = code

class AbsenceValidator:
    """
    Validateur complet pour les données d'absence.
    Gère la validation des champs, contraintes métier et règles de gestion.
    """
    
    # Types d'absence autorisés avec leurs contraintes
    TYPES_ABSENCE = {
        'CONGE_PAYE': {
            'label': 'Congé payé',
            'max_duration_days': 30,
            'requires_approval': True,
            'impact_paie_default': False
        },
        'CONGE_SANS_SOLDE': {
            'label': 'Congé sans solde',
            'max_duration_days': 365,
            'requires_approval': True,
            'impact_paie_default': True
        },
        'MALADIE': {
            'label': 'Arrêt maladie',
            'max_duration_days': 180,
            'requires_approval': False,
            'impact_paie_default': True
        },
        'FORMATION': {
            'label': 'Formation',
            'max_duration_days': 60,
            'requires_approval': True,
            'impact_paie_default': False
        },
        'MATERNITE': {
            'label': 'Congé maternité',
            'max_duration_days': 120,
            'requires_approval': False,
            'impact_paie_default': False
        },
        'PATERNITE': {
            'label': 'Congé paternité',
            'max_duration_days': 14,
            'requires_approval': False,
            'impact_paie_default': False
        },
        'AUTRE': {
            'label': 'Autre',
            'max_duration_days': 30,
            'requires_approval': True,
            'impact_paie_default': True
        }
    }
    
    # Configuration de validation
    MIN_ADVANCE_NOTICE_DAYS = getattr(settings, 'ABSENCE_MIN_ADVANCE_NOTICE_DAYS', 1)
    MAX_FUTURE_BOOKING_DAYS = getattr(settings, 'ABSENCE_MAX_FUTURE_BOOKING_DAYS', 365)
    
    def __init__(self):
        self.errors = []
    
    def validate_absence_data(self, data: Dict[str, Any], is_update: bool = False) -> Dict[str, Any]:
        """
        Valide complètement les données d'une absence.
        
        Args:
            data: Données à valider
            is_update: True si c'est une mise à jour (certains champs peuvent être optionnels)
            
        Returns:
            Dict des données validées et nettoyées
            
        Raises:
            ValidationError: Si les données sont invalides
        """
        self.errors = []
        
        try:
            # Validation de base
            validated_data = self._validate_basic_fields(data, is_update)
            
            # Validation des dates
            self._validate_dates(validated_data)
            
            # Validation du type d'absence
            self._validate_type_absence(validated_data)
            
            # Validation de l'employé
            self._validate_employe(validated_data)
            
            # Validation des contraintes métier
            self._validate_business_rules(validated_data)
            
            # Nettoyage et enrichissement des données
            validated_data = self._clean_and_enrich_data(validated_data)
            
            if self.errors:
                raise ValidationError(f"Erreurs de validation: {'; '.join(self.errors)}")
            
            return validated_data
            
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"Erreur inattendue lors de la validation: {e}")
            raise ValidationError(f"Erreur de validation: {str(e)}")
    
    def _validate_basic_fields(self, data: Dict[str, Any], is_update: bool) -> Dict[str, Any]:
        """Valide les champs de base requis."""
        if not isinstance(data, dict):
            raise ValidationError("Les données doivent être un dictionnaire")
        
        validated = {}
        
        # Champs requis pour création
        if not is_update:
            required_fields = ['employe_id', 'date_debut', 'date_fin', 'type_absence']
            for field in required_fields:
                if field not in data or data[field] is None or data[field] == '':
                    self.errors.append(f"Le champ '{field}' est requis")
                else:
                    validated[field] = data[field]
        else:
            # Pour les mises à jour, copier les champs présents
            for field in ['employe_id', 'date_debut', 'date_fin', 'type_absence']:
                if field in data and data[field] is not None and data[field] != '':
                    validated[field] = data[field]
        
        # Champs optionnels
        optional_fields = ['motif', 'impact_paie', 'commentaire', 'justificatif_url']
        for field in optional_fields:
            if field in data:
                validated[field] = data[field]
        
        return validated
    
    def _validate_dates(self, data: Dict[str, Any]) -> None:
        """Valide les dates de début et fin."""
        date_debut_str = data.get('date_debut')
        date_fin_str = data.get('date_fin')
        
        if not date_debut_str or not date_fin_str:
            return  # Sera géré par la validation des champs requis
        
        # Validation du format
        try:
            date_debut = datetime.strptime(date_debut_str, '%Y-%m-%d').date()
        except ValueError:
            self.errors.append("Format de date_debut invalide (attendu: YYYY-MM-DD)")
            return
        
        try:
            date_fin = datetime.strptime(date_fin_str, '%Y-%m-%d').date()
        except ValueError:
            self.errors.append("Format de date_fin invalide (attendu: YYYY-MM-DD)")
            return
        
        # Validation de la cohérence des dates
        if date_debut > date_fin:
            self.errors.append("La date de début ne peut pas être postérieure à la date de fin")
        
        # Validation par rapport à aujourd'hui
        today = date.today()
        
        # Vérification du préavis minimum (sauf pour maladie)
        type_absence = data.get('type_absence')
        if type_absence != 'MALADIE':
            min_date = today + timedelta(days=self.MIN_ADVANCE_NOTICE_DAYS)
            if date_debut < min_date:
                self.errors.append(f"Un préavis de {self.MIN_ADVANCE_NOTICE_DAYS} jour(s) minimum est requis")
        
        # Vérification de la limite de réservation future
        max_date = today + timedelta(days=self.MAX_FUTURE_BOOKING_DAYS)
        if date_debut > max_date:
            self.errors.append(f"Impossible de réserver une absence plus de {self.MAX_FUTURE_BOOKING_DAYS} jours à l'avance")
        
        # Validation de la durée
        duration = (date_fin - date_debut).days + 1
        if duration <= 0:
            self.errors.append("La durée de l'absence doit être d'au moins 1 jour")
        elif duration > 365:
            self.errors.append("La durée de l'absence ne peut pas dépasser 365 jours")
    
    def _validate_type_absence(self, data: Dict[str, Any]) -> None:
        """Valide le type d'absence et ses contraintes."""
        type_absence = data.get('type_absence')
        
        if not type_absence:
            return
        
        if type_absence not in self.TYPES_ABSENCE:
            self.errors.append(f"Type d'absence invalide: {type_absence}")
            return
        
        # Validation de la durée selon le type
        date_debut_str = data.get('date_debut')
        date_fin_str = data.get('date_fin')
        
        if date_debut_str and date_fin_str:
            try:
                date_debut = datetime.strptime(date_debut_str, '%Y-%m-%d').date()
                date_fin = datetime.strptime(date_fin_str, '%Y-%m-%d').date()
                duration = (date_fin - date_debut).days + 1
                
                max_duration = self.TYPES_ABSENCE[type_absence]['max_duration_days']
                if duration > max_duration:
                    self.errors.append(
                        f"La durée maximale pour {self.TYPES_ABSENCE[type_absence]['label']} "
                        f"est de {max_duration} jours"
                    )
            except ValueError:
                pass  # Erreur de format déjà gérée ailleurs
    
    def _validate_employe(self, data: Dict[str, Any]) -> None:
        """Valide l'ID de l'employé."""
        employe_id = data.get('employe_id')
        
        if not employe_id:
            return
        
        # Validation du format UUID (basique)
        if not isinstance(employe_id, str):
            self.errors.append("L'ID employé doit être une chaîne de caractères")
            return
        
        # Validation du format UUID plus stricte
        uuid_pattern = re.compile(
            r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$',
            re.IGNORECASE
        )
        
        if not uuid_pattern.match(employe_id):
            self.errors.append("Format d'ID employé invalide (UUID attendu)")
    
    def _validate_business_rules(self, data: Dict[str, Any]) -> None:
        """Valide les règles métier spécifiques."""
        type_absence = data.get('type_absence')
        motif = data.get('motif', '').strip()
        
        # Certains types d'absence nécessitent un motif
        if type_absence in ['AUTRE', 'CONGE_SANS_SOLDE'] and not motif:
            self.errors.append(f"Un motif est requis pour le type d'absence '{type_absence}'")
        
        # Validation de la longueur du motif
        if motif and len(motif) > 500:
            self.errors.append("Le motif ne peut pas dépasser 500 caractères")
        
        # Validation du commentaire
        commentaire = data.get('commentaire', '').strip()
        if commentaire and len(commentaire) > 1000:
            self.errors.append("Le commentaire ne peut pas dépasser 1000 caractères")
    
    def _clean_and_enrich_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Nettoie et enrichit les données validées."""
        cleaned = {}
        
        # Copier les champs validés
        for key, value in data.items():
            if value is not None and value != '':
                if isinstance(value, str):
                    cleaned[key] = value.strip()
                else:
                    cleaned[key] = value
        
        # Enrichissement automatique
        type_absence = cleaned.get('type_absence')
        if type_absence and type_absence in self.TYPES_ABSENCE:
            type_config = self.TYPES_ABSENCE[type_absence]
            
            # Définir impact_paie par défaut si non spécifié
            if 'impact_paie' not in cleaned:
                cleaned['impact_paie'] = type_config['impact_paie_default']
            
            # Ajouter des métadonnées
            cleaned['_type_config'] = type_config
        
        # Valeurs par défaut
        cleaned.setdefault('impact_paie', False)
        cleaned.setdefault('motif', '')
        
        return cleaned
    
    def validate_date_range(self, date_debut: str, date_fin: str) -> bool:
        """
        Valide une plage de dates (utilitaire).
        
        Args:
            date_debut: Date de début au format YYYY-MM-DD
            date_fin: Date de fin au format YYYY-MM-DD
            
        Returns:
            True si valide, False sinon
        """
        try:
            self._validate_dates({'date_debut': date_debut, 'date_fin': date_fin})
            return len(self.errors) == 0
        except Exception:
            return False
    
    def get_type_absence_info(self, type_absence: str) -> Optional[Dict[str, Any]]:
        """
        Retourne les informations d'un type d'absence.
        
        Args:
            type_absence: Code du type d'absence
            
        Returns:
            Dict avec les informations du type ou None si invalide
        """
        return self.TYPES_ABSENCE.get(type_absence)
    
    def get_available_types(self) -> Dict[str, str]:
        """
        Retourne la liste des types d'absence disponibles.
        
        Returns:
            Dict {code: label}
        """
        return {code: info['label'] for code, info in self.TYPES_ABSENCE.items()}

# Instance globale du validateur
default_validator = AbsenceValidator()
