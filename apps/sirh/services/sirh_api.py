import os
import requests

SIRH_API_URL = os.environ.get('SIRH_API_URL', 'http://localhost:3000')

class SirhApiService:
    """
    Service d'intégration avec l'API SIRH (paie, employés, prêts, avances, etc.)
    """

    def __init__(self, base_url=None):
        self.base_url = base_url or SIRH_API_URL

    def get_employes(self, params=None):
        """Récupère la liste des employés depuis l'API SIRH."""
        url = f"{self.base_url}/api/employes"
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()

    def get_entreprises(self, params=None):
        """Récupère la liste des entreprises."""
        url = f"{self.base_url}/api/entreprises"
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()

    def create_entreprise(self, data):
        """Crée une nouvelle entreprise."""
        url = f"{self.base_url}/api/entreprises"
        response = requests.post(url, json=data)
        response.raise_for_status()
        return response.json()

    def get_entreprise(self, entreprise_id):
        url = f"{self.base_url}/api/entreprises/{entreprise_id}"
        response = requests.get(url)
        response.raise_for_status()
        return response.json()

    def get_employes_entreprise(self, entreprise_id, params=None):
        url = f"{self.base_url}/api/entreprises/{entreprise_id}/employes"
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()

    def get_statistiques_entreprise(self, entreprise_id, params=None):
        url = f"{self.base_url}/api/entreprises/{entreprise_id}/statistiques"
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()

    def create_employe(self, data):
        url = f"{self.base_url}/api/employes"
        response = requests.post(url, json=data)
        response.raise_for_status()
        return response.json()

    def get_employe(self, employe_id):
        url = f"{self.base_url}/api/employes/{employe_id}"
        response = requests.get(url)
        response.raise_for_status()
        return response.json()

    def update_employe(self, employe_id, data):
        url = f"{self.base_url}/api/employes/{employe_id}"
        response = requests.put(url, json=data)
        response.raise_for_status()
        return response.json()

    def get_bulletins_employe(self, employe_id, params=None):
        url = f"{self.base_url}/api/employes/{employe_id}/bulletins"
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()

    def generer_bulletin_employe(self, employe_id, data):
        url = f"{self.base_url}/api/employes/{employe_id}/generer-bulletin"
        response = requests.post(url, json=data)
        response.raise_for_status()
        return response.json()

    def calculer_paie(self, data):
        url = f"{self.base_url}/api/paie/calculer"
        response = requests.post(url, json=data)
        response.raise_for_status()
        return response.json()

    def create_bulletin(self, data):
        url = f"{self.base_url}/api/paie/bulletins"
        response = requests.post(url, json=data)
        response.raise_for_status()
        return response.json()

    def get_bulletin(self, bulletin_id):
        url = f"{self.base_url}/api/paie/bulletins/{bulletin_id}"
        response = requests.get(url)
        response.raise_for_status()
        return response.json()

    def get_bulletins(self, params=None):
        url = f"{self.base_url}/api/paie/bulletins"
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()

    def update_bulletin_statut(self, bulletin_id, data):
        url = f"{self.base_url}/api/paie/bulletins/{bulletin_id}/statut"
        response = requests.patch(url, json=data)
        response.raise_for_status()
        return response.json()

    def export_bulletin_pdf(self, bulletin_id):
        url = f"{self.base_url}/api/paie/bulletins/{bulletin_id}/pdf"
        response = requests.get(url)
        response.raise_for_status()
        return response.content  # PDF binaire

    def get_prets(self, params=None):
        url = f"{self.base_url}/api/prets"
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()

    def get_pret(self, pret_id):
        url = f"{self.base_url}/api/prets/{pret_id}"
        response = requests.get(url)
        response.raise_for_status()
        return response.json()

    def simuler_pret(self, data):
        url = f"{self.base_url}/api/prets/simuler"
        response = requests.post(url, json=data)
        response.raise_for_status()
        return response.json()

    def create_pret(self, data):
        url = f"{self.base_url}/api/prets"
        response = requests.post(url, json=data)
        response.raise_for_status()
        return response.json()

    def remboursement_pret(self, pret_id, data):
        url = f"{self.base_url}/api/prets/{pret_id}/remboursement"
        response = requests.post(url, json=data)
        response.raise_for_status()
        return response.json()

    def update_pret_statut(self, pret_id, data):
        url = f"{self.base_url}/api/prets/{pret_id}/statut"
        response = requests.put(url, json=data)
        response.raise_for_status()
        return response.json()

    def get_avances(self, params=None):
        url = f"{self.base_url}/api/avances"
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()

    def get_avance(self, avance_id):
        url = f"{self.base_url}/api/avances/{avance_id}"
        response = requests.get(url)
        response.raise_for_status()
        return response.json()

    def create_avance(self, data):
        url = f"{self.base_url}/api/avances"
        response = requests.post(url, json=data)
        response.raise_for_status()
        return response.json()

    def approuver_avance(self, avance_id):
        url = f"{self.base_url}/api/avances/{avance_id}/approuver"
        response = requests.post(url)
        response.raise_for_status()
        return response.json()

    def remboursement_avance(self, avance_id, data):
        url = f"{self.base_url}/api/avances/{avance_id}/remboursement"
        response = requests.post(url, json=data)
        response.raise_for_status()
        return response.json()

    def delete_avance(self, avance_id):
        url = f"{self.base_url}/api/avances/{avance_id}"
        response = requests.delete(url)
        response.raise_for_status()
        return response.json()

    def get_pointages(self, params=None):
        url = f"{self.base_url}/api/pointages"
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()

    def create_pointage(self, data):
        url = f"{self.base_url}/api/pointages"
        response = requests.post(url, json=data)
        response.raise_for_status()
        return response.json()

    def get_absences(self, params=None):
        url = f"{self.base_url}/api/absences"
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()

    def create_absence(self, data):
        url = f"{self.base_url}/api/absences"
        response = requests.post(url, json=data)
        response.raise_for_status()
        return response.json()

    def approuver_absence(self, absence_id, data=None):
        url = f"{self.base_url}/api/absences/{absence_id}/approuver"
        response = requests.post(url, json=data or {})
        response.raise_for_status()
        return response.json()

    def get_heures_supplementaires(self, params=None):
        url = f"{self.base_url}/api/heures-supplementaires"
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()

    def calculer_heures_supplementaires(self, data):
        url = f"{self.base_url}/api/heures-supplementaires/calculer"
        response = requests.post(url, json=data)
        response.raise_for_status()
        return response.json()

    def approuver_heures_supplementaires(self, data):
        url = f"{self.base_url}/api/heures-supplementaires/approuver"
        response = requests.post(url, json=data)
        response.raise_for_status()
        return response.json()

    def get_baremes_irpp(self, annee, type_):
        url = f"{self.base_url}/api/paie/baremes/{annee}/{type_}"
        response = requests.get(url)
        response.raise_for_status()
        return response.json()

    def get_parametres_paie(self, annee):
        url = f"{self.base_url}/api/paie/parametres/{annee}"
        response = requests.get(url)
        response.raise_for_status()
        return response.json()

    # === MÉTHODES ÉTENDUES POUR LES ABSENCES ===

    def get_absence(self, absence_id):
        """Récupère le détail d'une absence par son ID."""
        url = f"{self.base_url}/api/absences/{absence_id}"
        response = requests.get(url)
        response.raise_for_status()
        return response.json()

    def update_absence(self, absence_id, data):
        """Met à jour une absence existante."""
        url = f"{self.base_url}/api/absences/{absence_id}"
        response = requests.put(url, json=data)
        response.raise_for_status()
        return response.json()

    def delete_absence(self, absence_id):
        """Supprime une absence."""
        url = f"{self.base_url}/api/absences/{absence_id}"
        response = requests.delete(url)
        response.raise_for_status()
        return response.json()

    def rejeter_absence(self, absence_id, data=None):
        """Rejette une absence avec motif optionnel."""
        url = f"{self.base_url}/api/absences/{absence_id}/rejeter"
        response = requests.post(url, json=data or {})
        response.raise_for_status()
        return response.json()

    def get_absences_employe(self, employe_id, params=None):
        """Récupère les absences d'un employé spécifique."""
        base_params = {"employeId": employe_id}
        if params:
            base_params.update(params)
        return self.get_absences(base_params)

    def get_absences_periode(self, date_debut, date_fin, params=None):
        """Récupère les absences sur une période donnée."""
        base_params = {
            "dateDebut": date_debut,
            "dateFin": date_fin
        }
        if params:
            base_params.update(params)
        return self.get_absences(base_params)

    def get_statistiques_absences(self, params=None):
        """Récupère les statistiques des absences."""
        url = f"{self.base_url}/api/absences/statistiques"
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()
