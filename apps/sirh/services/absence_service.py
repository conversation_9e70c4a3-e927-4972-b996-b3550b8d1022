"""
Service pour la gestion des absences (intégration SIRH, logique métier, validation)
"""

import logging
from datetime import datetime, date
from typing import Dict, List, Optional, Any
from django.conf import settings

from .sirh_api import SirhApiService
from .api_error_handler import ApiErro<PERSON><PERSON><PERSON><PERSON>, ApiError
from .absence_validator import AbsenceValidator, ValidationError
from .absence_cache import AbsenceCache

logger = logging.getLogger(__name__)

class AbsenceValidationError(Exception):
    """Exception levée lors d'erreurs de validation des données d'absence"""
    pass

class AbsenceService:
    """
    Service métier pour la gestion des absences.
    Encapsule l'API SIRH avec validation, cache et gestion d'erreurs robuste.
    """
    
    # Types d'absence supportés
    TYPES_ABSENCE = {
        'CONGE_PAYE': 'Congé payé',
        'CONGE_SANS_SOLDE': 'Congé sans solde',
        'MALADIE': 'Arrêt maladie',
        'FORMATION': 'Formation',
        'MATERNITE': 'Congé maternité',
        'PATERNITE': 'Congé paternité',
        'AUTRE': 'Autre'
    }
    
    # Durée de cache en secondes (5 minutes)
    CACHE_TIMEOUT = getattr(settings, 'ABSENCE_CACHE_TIMEOUT', 300)
    
    def __init__(self):
        self.sirh = SirhApiService()
        self.validator = AbsenceValidator()
        self.cache = AbsenceCache()
        self.error_handler = ApiErrorHandler()
    
    def get_absences(self, employe_id: Optional[str] = None, 
                    date_debut: Optional[str] = None, 
                    date_fin: Optional[str] = None,
                    type_absence: Optional[str] = None,
                    use_cache: bool = True) -> Dict[str, Any]:
        """
        Récupère la liste des absences avec filtres optionnels.
        
        Args:
            employe_id: UUID de l'employé (optionnel)
            date_debut: Date de début au format YYYY-MM-DD (optionnel)
            date_fin: Date de fin au format YYYY-MM-DD (optionnel)
            type_absence: Type d'absence (optionnel)
            use_cache: Utiliser le cache (défaut: True)
            
        Returns:
            Dict contenant la liste des absences et métadonnées
            
        Raises:
            AbsenceValidationError: Si les paramètres sont invalides
            Exception: Si erreur API
        """
        try:
            # Validation des paramètres avec le validateur
            if date_debut and date_fin:
                if not self.validator.validate_date_range(date_debut, date_fin):
                    raise AbsenceValidationError("Plage de dates invalide")

            if type_absence and type_absence not in self.TYPES_ABSENCE:
                raise AbsenceValidationError(f"Type d'absence invalide: {type_absence}")

            # Construction des paramètres de requête
            params = {}
            if employe_id:
                params['employeId'] = employe_id
            if date_debut:
                params['dateDebut'] = date_debut
            if date_fin:
                params['dateFin'] = date_fin
            if type_absence:
                params['type_absence'] = type_absence

            # Utiliser le cache intelligent
            if use_cache:
                return self.cache.get_absences_list(params, lambda p: self.sirh.get_absences(p))
            else:
                # Appel API direct
                logger.info(f"Récupération des absences avec paramètres: {params}")
                result = self.sirh.get_absences(params)
            
                # Enrichissement des données
                if 'data' in result and isinstance(result['data'], list):
                    for absence in result['data']:
                        absence['type_absence_label'] = self.TYPES_ABSENCE.get(
                            absence.get('type_absence'),
                            absence.get('type_absence', 'Inconnu')
                        )

                return result
            
        except AbsenceValidationError:
            raise
        except ApiError as e:
            logger.error(f"Erreur API lors de la récupération des absences: {e}")
            raise Exception(f"Erreur API lors de la récupération des absences: {e}")
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des absences: {e}")
            raise Exception(f"Erreur lors de la récupération des absences: {e}")
    
    def get_absence(self, absence_id: str) -> Dict[str, Any]:
        """
        Récupère le détail d'une absence par son ID.
        
        Args:
            absence_id: UUID de l'absence
            
        Returns:
            Dict contenant les détails de l'absence
            
        Raises:
            AbsenceValidationError: Si l'ID est invalide
            Exception: Si erreur API
        """
        try:
            if not absence_id or not isinstance(absence_id, str):
                raise AbsenceValidationError("ID d'absence requis et doit être une chaîne")

            # Utiliser le cache intelligent
            def fetcher(aid):
                logger.info(f"Récupération de l'absence {aid}")
                # Utiliser la nouvelle méthode get_absence du SirhApiService
                try:
                    result = self.sirh.get_absence(aid)
                except Exception:
                    # Fallback vers get_absences avec filtre
                    list_result = self.sirh.get_absences({'id': aid})
                    if 'data' in list_result and isinstance(list_result['data'], list) and list_result['data']:
                        result = list_result['data'][0]
                    else:
                        raise Exception(f"Absence {aid} non trouvée")

                # Enrichissement
                result['type_absence_label'] = self.TYPES_ABSENCE.get(
                    result.get('type_absence'),
                    result.get('type_absence', 'Inconnu')
                )
                return result

            return self.cache.get_absence_detail(absence_id, fetcher)
                
        except AbsenceValidationError:
            raise
        except Exception as e:
            logger.error(f"Erreur lors de la récupération de l'absence {absence_id}: {e}")
            raise Exception(f"Erreur lors de la récupération de l'absence: {e}")
    
    def create_absence(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Crée une nouvelle absence après validation.
        
        Args:
            data: Données de l'absence à créer
            
        Returns:
            Dict contenant l'absence créée
            
        Raises:
            AbsenceValidationError: Si les données sont invalides
            Exception: Si erreur API
        """
        try:
            # Validation des données avec le validateur
            validated_data = self.validator.validate_absence_data(data, is_update=False)

            logger.info(f"Création d'une absence pour l'employé {validated_data['employe_id']}")

            # Appel API
            result = self.sirh.create_absence(validated_data)

            # Invalider le cache
            self.cache.invalidate_employe_absences(validated_data['employe_id'])

            logger.info(f"Absence créée avec succès: {result.get('id', 'ID non disponible')}")
            return result
            
        except (AbsenceValidationError, ValidationError):
            raise
        except ApiError as e:
            logger.error(f"Erreur API lors de la création de l'absence: {e}")
            raise Exception(f"Erreur API lors de la création de l'absence: {e}")
        except Exception as e:
            logger.error(f"Erreur lors de la création de l'absence: {e}")
            raise Exception(f"Erreur lors de la création de l'absence: {e}")
    
    def approve_absence(self, absence_id: str, approuve_par: Optional[str] = None) -> Dict[str, Any]:
        """
        Approuve une absence.
        
        Args:
            absence_id: UUID de l'absence à approuver
            approuve_par: Nom/ID de la personne qui approuve (optionnel)
            
        Returns:
            Dict contenant le résultat de l'approbation
            
        Raises:
            AbsenceValidationError: Si l'ID est invalide
            Exception: Si erreur API
        """
        try:
            if not absence_id or not isinstance(absence_id, str):
                raise AbsenceValidationError("ID d'absence requis pour l'approbation")
            
            data = {}
            if approuve_par:
                data['approuve_par'] = approuve_par
            
            logger.info(f"Approbation de l'absence {absence_id}")
            
            # Appel API
            result = self.sirh.approuver_absence(absence_id, data)

            # Invalider le cache
            self.cache.invalidate_absence(absence_id)

            logger.info(f"Absence {absence_id} approuvée avec succès")
            return result
            
        except AbsenceValidationError:
            raise
        except Exception as e:
            logger.error(f"Erreur lors de l'approbation de l'absence {absence_id}: {e}")
            raise Exception(f"Erreur lors de l'approbation de l'absence: {e}")
    
    def get_employes(self) -> Dict[str, Any]:
        """
        Récupère la liste des employés pour les formulaires.
        
        Returns:
            Dict contenant la liste des employés
        """
        try:
            # Utiliser le cache intelligent pour les employés
            return self.cache.get_employes_list(lambda: self.sirh.get_employes())
            
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des employés: {e}")
            raise Exception(f"Erreur lors de la récupération des employés: {e}")
    
    def get_types_absence(self) -> Dict[str, str]:
        """
        Retourne les types d'absence disponibles.

        Returns:
            Dict des types d'absence {code: label}
        """
        return self.validator.get_available_types()

    def update_absence(self, absence_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Met à jour une absence existante.

        Args:
            absence_id: UUID de l'absence à modifier
            data: Données de mise à jour

        Returns:
            Dict contenant l'absence mise à jour

        Raises:
            AbsenceValidationError: Si les données sont invalides
            Exception: Si erreur API
        """
        try:
            if not absence_id or not isinstance(absence_id, str):
                raise AbsenceValidationError("ID d'absence requis pour la mise à jour")

            # Validation des données avec le validateur (mode update)
            validated_data = self.validator.validate_absence_data(data, is_update=True)

            logger.info(f"Mise à jour de l'absence {absence_id}")

            # Appel API
            result = self.sirh.update_absence(absence_id, validated_data)

            # Invalider le cache
            self.cache.invalidate_absence(absence_id)

            logger.info(f"Absence {absence_id} mise à jour avec succès")
            return result

        except (AbsenceValidationError, ValidationError):
            raise
        except ApiError as e:
            logger.error(f"Erreur API lors de la mise à jour de l'absence {absence_id}: {e}")
            raise Exception(f"Erreur API lors de la mise à jour de l'absence: {e}")
        except Exception as e:
            logger.error(f"Erreur lors de la mise à jour de l'absence {absence_id}: {e}")
            raise Exception(f"Erreur lors de la mise à jour de l'absence: {e}")

    def delete_absence(self, absence_id: str) -> Dict[str, Any]:
        """
        Supprime une absence.

        Args:
            absence_id: UUID de l'absence à supprimer

        Returns:
            Dict contenant le résultat de la suppression

        Raises:
            AbsenceValidationError: Si l'ID est invalide
            Exception: Si erreur API
        """
        try:
            if not absence_id or not isinstance(absence_id, str):
                raise AbsenceValidationError("ID d'absence requis pour la suppression")

            logger.info(f"Suppression de l'absence {absence_id}")

            # Appel API
            result = self.sirh.delete_absence(absence_id)

            # Invalider le cache
            self.cache.invalidate_absence(absence_id)

            logger.info(f"Absence {absence_id} supprimée avec succès")
            return result

        except AbsenceValidationError:
            raise
        except ApiError as e:
            logger.error(f"Erreur API lors de la suppression de l'absence {absence_id}: {e}")
            raise Exception(f"Erreur API lors de la suppression de l'absence: {e}")
        except Exception as e:
            logger.error(f"Erreur lors de la suppression de l'absence {absence_id}: {e}")
            raise Exception(f"Erreur lors de la suppression de l'absence: {e}")

    def reject_absence(self, absence_id: str, motif_rejet: Optional[str] = None) -> Dict[str, Any]:
        """
        Rejette une absence.

        Args:
            absence_id: UUID de l'absence à rejeter
            motif_rejet: Motif du rejet (optionnel)

        Returns:
            Dict contenant le résultat du rejet

        Raises:
            AbsenceValidationError: Si l'ID est invalide
            Exception: Si erreur API
        """
        try:
            if not absence_id or not isinstance(absence_id, str):
                raise AbsenceValidationError("ID d'absence requis pour le rejet")

            data = {}
            if motif_rejet:
                data['motif_rejet'] = motif_rejet.strip()

            logger.info(f"Rejet de l'absence {absence_id}")

            # Appel API
            result = self.sirh.rejeter_absence(absence_id, data)

            # Invalider le cache
            self.cache.invalidate_absence(absence_id)

            logger.info(f"Absence {absence_id} rejetée avec succès")
            return result

        except AbsenceValidationError:
            raise
        except ApiError as e:
            logger.error(f"Erreur API lors du rejet de l'absence {absence_id}: {e}")
            raise Exception(f"Erreur API lors du rejet de l'absence: {e}")
        except Exception as e:
            logger.error(f"Erreur lors du rejet de l'absence {absence_id}: {e}")
            raise Exception(f"Erreur lors du rejet de l'absence: {e}")

    def get_cache_stats(self) -> Dict[str, Any]:
        """
        Retourne les statistiques du cache.

        Returns:
            Dict avec les statistiques de cache
        """
        return self.cache.get_cache_stats()

    def warm_up_cache(self) -> Dict[str, Any]:
        """
        Préchauffe le cache avec les données courantes.

        Returns:
            Dict avec le résultat du préchauffage
        """
        return self.cache.warm_up_cache(
            employes_fetcher=lambda: self.sirh.get_employes(),
            recent_absences_fetcher=lambda params: self.sirh.get_absences(params)
        )
