"""
Gestionnaire d'erreurs robuste pour les appels API SIRH.
Gère les timeouts, erreurs HTTP, données invalides et retry logic.
"""

import logging
import time
from typing import Dict, Any, Optional, Callable
from functools import wraps
import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry
from django.conf import settings

logger = logging.getLogger(__name__)

class ApiError(Exception):
    """Exception de base pour les erreurs API"""
    def __init__(self, message: str, status_code: Optional[int] = None, response_data: Optional[Dict] = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_data = response_data or {}

class ApiTimeoutError(ApiError):
    """Exception pour les timeouts API"""
    pass

class ApiValidationError(ApiError):
    """Exception pour les erreurs de validation côté API"""
    pass

class ApiConnectionError(ApiError):
    """Exception pour les erreurs de connexion"""
    pass

class ApiServerError(ApiError):
    """Exception pour les erreurs serveur (5xx)"""
    pass

class ApiClientError(ApiError):
    """Exception pour les erreurs client (4xx)"""
    pass

class ApiErrorHandler:
    """
    Gestionnaire centralisé des erreurs API avec retry logic et timeouts configurables.
    """
    
    # Configuration par défaut
    DEFAULT_TIMEOUT = getattr(settings, 'SIRH_API_TIMEOUT', 30)
    DEFAULT_RETRY_ATTEMPTS = getattr(settings, 'SIRH_API_RETRY_ATTEMPTS', 3)
    DEFAULT_RETRY_BACKOFF = getattr(settings, 'SIRH_API_RETRY_BACKOFF', 1)
    
    # Codes d'erreur à retry
    RETRY_STATUS_CODES = [429, 500, 502, 503, 504]
    
    def __init__(self, timeout: int = None, retry_attempts: int = None, retry_backoff: float = None):
        self.timeout = timeout or self.DEFAULT_TIMEOUT
        self.retry_attempts = retry_attempts or self.DEFAULT_RETRY_ATTEMPTS
        self.retry_backoff = retry_backoff or self.DEFAULT_RETRY_BACKOFF
        
        # Configuration de la session avec retry automatique
        self.session = self._create_session()
    
    def _create_session(self) -> requests.Session:
        """
        Crée une session requests avec retry automatique configuré.
        """
        session = requests.Session()
        
        # Configuration du retry automatique
        retry_strategy = Retry(
            total=self.retry_attempts,
            status_forcelist=self.RETRY_STATUS_CODES,
            method_whitelist=["HEAD", "GET", "OPTIONS"],
            backoff_factor=self.retry_backoff
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    def handle_request(self, method: str, url: str, **kwargs) -> Dict[str, Any]:
        """
        Exécute une requête HTTP avec gestion d'erreurs complète.
        
        Args:
            method: Méthode HTTP (GET, POST, PUT, DELETE, etc.)
            url: URL de la requête
            **kwargs: Arguments supplémentaires pour requests
            
        Returns:
            Dict contenant la réponse JSON
            
        Raises:
            ApiError: En cas d'erreur API
        """
        # Ajouter le timeout par défaut si non spécifié
        kwargs.setdefault('timeout', self.timeout)
        
        start_time = time.time()
        attempt = 0
        last_exception = None
        
        while attempt < self.retry_attempts:
            attempt += 1
            
            try:
                logger.debug(f"Tentative {attempt}/{self.retry_attempts} - {method} {url}")
                
                # Exécuter la requête
                response = self.session.request(method, url, **kwargs)
                
                # Mesurer le temps de réponse
                response_time = time.time() - start_time
                logger.debug(f"Réponse reçue en {response_time:.2f}s - Status: {response.status_code}")
                
                # Traiter la réponse
                return self._handle_response(response)
                
            except requests.exceptions.Timeout as e:
                last_exception = ApiTimeoutError(
                    f"Timeout lors de l'appel API ({self.timeout}s): {url}",
                    response_data={'timeout': self.timeout, 'attempt': attempt}
                )
                logger.warning(f"Timeout tentative {attempt}: {e}")
                
            except requests.exceptions.ConnectionError as e:
                last_exception = ApiConnectionError(
                    f"Erreur de connexion à l'API: {url}",
                    response_data={'attempt': attempt, 'error': str(e)}
                )
                logger.warning(f"Erreur de connexion tentative {attempt}: {e}")
                
            except requests.exceptions.RequestException as e:
                last_exception = ApiError(
                    f"Erreur de requête: {str(e)}",
                    response_data={'attempt': attempt, 'error': str(e)}
                )
                logger.warning(f"Erreur de requête tentative {attempt}: {e}")
            
            # Attendre avant la prochaine tentative (sauf pour la dernière)
            if attempt < self.retry_attempts:
                wait_time = self.retry_backoff * (2 ** (attempt - 1))  # Backoff exponentiel
                logger.debug(f"Attente de {wait_time}s avant la prochaine tentative")
                time.sleep(wait_time)
        
        # Toutes les tentatives ont échoué
        logger.error(f"Échec de toutes les tentatives pour {method} {url}")
        raise last_exception
    
    def _handle_response(self, response: requests.Response) -> Dict[str, Any]:
        """
        Traite la réponse HTTP et gère les erreurs.
        
        Args:
            response: Objet Response de requests
            
        Returns:
            Dict contenant les données JSON
            
        Raises:
            ApiError: En cas d'erreur HTTP ou de données invalides
        """
        try:
            # Tenter de parser le JSON
            if response.content:
                data = response.json()
            else:
                data = {}
                
        except ValueError as e:
            # Réponse non-JSON
            raise ApiError(
                f"Réponse API invalide (non-JSON): {response.text[:200]}",
                status_code=response.status_code,
                response_data={'raw_response': response.text[:500]}
            )
        
        # Vérifier le code de statut
        if response.status_code >= 500:
            raise ApiServerError(
                f"Erreur serveur API (HTTP {response.status_code}): {data.get('message', 'Erreur inconnue')}",
                status_code=response.status_code,
                response_data=data
            )
        
        elif response.status_code >= 400:
            error_message = data.get('message') or data.get('error') or f"Erreur client HTTP {response.status_code}"
            
            if response.status_code == 400:
                raise ApiValidationError(
                    f"Erreur de validation: {error_message}",
                    status_code=response.status_code,
                    response_data=data
                )
            elif response.status_code == 404:
                raise ApiClientError(
                    f"Ressource non trouvée: {error_message}",
                    status_code=response.status_code,
                    response_data=data
                )
            else:
                raise ApiClientError(
                    error_message,
                    status_code=response.status_code,
                    response_data=data
                )
        
        elif not (200 <= response.status_code < 300):
            raise ApiError(
                f"Code de statut inattendu: {response.status_code}",
                status_code=response.status_code,
                response_data=data
            )
        
        return data
    
    def get(self, url: str, **kwargs) -> Dict[str, Any]:
        """Exécute une requête GET avec gestion d'erreurs."""
        return self.handle_request('GET', url, **kwargs)
    
    def post(self, url: str, **kwargs) -> Dict[str, Any]:
        """Exécute une requête POST avec gestion d'erreurs."""
        return self.handle_request('POST', url, **kwargs)
    
    def put(self, url: str, **kwargs) -> Dict[str, Any]:
        """Exécute une requête PUT avec gestion d'erreurs."""
        return self.handle_request('PUT', url, **kwargs)
    
    def patch(self, url: str, **kwargs) -> Dict[str, Any]:
        """Exécute une requête PATCH avec gestion d'erreurs."""
        return self.handle_request('PATCH', url, **kwargs)
    
    def delete(self, url: str, **kwargs) -> Dict[str, Any]:
        """Exécute une requête DELETE avec gestion d'erreurs."""
        return self.handle_request('DELETE', url, **kwargs)

def with_error_handling(timeout: int = None, retry_attempts: int = None):
    """
    Décorateur pour ajouter la gestion d'erreurs automatique aux méthodes d'API.
    
    Args:
        timeout: Timeout personnalisé pour cette méthode
        retry_attempts: Nombre de tentatives personnalisé
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            # Créer un gestionnaire d'erreurs pour cette méthode
            error_handler = ApiErrorHandler(
                timeout=timeout,
                retry_attempts=retry_attempts
            )
            
            # Injecter le gestionnaire d'erreurs dans la méthode
            return func(self, error_handler, *args, **kwargs)
        
        return wrapper
    return decorator

# Instance globale du gestionnaire d'erreurs
default_error_handler = ApiErrorHandler()
