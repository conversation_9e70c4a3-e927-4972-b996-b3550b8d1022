{% extends "base/base.html" %}
{% load static %}

{% block content %}
<div class="main-wrapper">
    {% include 'includes/header.html' %}
    {% include 'includes/sidebar.html' %}
    <div class="page-wrapper">
        <div class="container mt-4">
            <h1 class="mb-4">Ajouter un employé</h1>
            <div id="employe-add-error" class="alert alert-danger" style="display:none;"></div>
            <form id="employe-add-form" class="mb-4" enctype="multipart/form-data">
                <div class="row">
                    <!-- Colonne gauche : Informations personnelles -->
                    <div class="col-md-6">
                        <h5 class="mb-3 mt-2">Informations personnelles</h5>
                        <div class="mb-3">
                            <label for="matricule" class="form-label">Matricule</label>
                            <input type="text" class="form-control" id="matricule" name="matricule" required>
                        </div>
                        <div class="mb-3">
                            <label for="nom" class="form-label">Nom</label>
                            <input type="text" class="form-control" id="nom" name="nom" required>
                        </div>
                        <div class="mb-3">
                            <label for="prenom" class="form-label">Prénom</label>
                            <input type="text" class="form-control" id="prenom" name="prenom" required>
                        </div>
                        <div class="mb-3">
                            <label for="adresse" class="form-label">Adresse</label>
                            <input type="text" class="form-control" id="adresse" name="adresse">
                        </div>
                        <div class="mb-3">
                            <label for="civilite" class="form-label">Civilité</label>
                            <select class="form-select" id="civilite" name="civilite">
                                <option value="M">Monsieur</option>
                                <option value="Mme">Madame</option>
                                <option value="Mlle">Mademoiselle</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="date_naissance" class="form-label">Date de naissance</label>
                            <input type="date" class="form-control" id="date_naissance" name="date_naissance">
                        </div>
                        <div class="mb-3">
                            <label for="age_actuel" class="form-label">Âge actuel</label>
                            <input type="number" class="form-control" id="age_actuel" name="age_actuel" min="0">
                        </div>
                        <div class="mb-3">
                            <label for="age_retraite" class="form-label">Âge retraite</label>
                            <input type="number" class="form-control" id="age_retraite" name="age_retraite" min="0">
                        </div>
                        <div class="mb-3">
                            <label for="date_retraite" class="form-label">Date retraite</label>
                            <input type="date" class="form-control" id="date_retraite" name="date_retraite">
                        </div>
                        <div class="mb-3">
                            <label for="type_piece_identite" class="form-label">Type de pièce d'identité</label>
                            <select class="form-select" id="type_piece_identite" name="type_piece_identite">
                                <option value="CIN">Carte d'identité</option>
                                <option value="PASSEPORT">Passeport</option>
                                <option value="AUTRE">Autre</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="numero_piece_identite" class="form-label">Numéro de la pièce</label>
                            <input type="text" class="form-control" id="numero_piece_identite" name="numero_piece_identite">
                        </div>
                        <div class="mb-3">
                            <label for="photo_piece_identite" class="form-label">Photo de la pièce</label>
                            <input type="file" class="form-control" id="photo_piece_identite" name="photo_piece_identite" accept="image/*">
                        </div>
                        <div class="mb-3">
                            <label for="carte_delivree_le" class="form-label">Carte délivrée le</label>
                            <input type="date" class="form-control" id="carte_delivree_le" name="carte_delivree_le">
                        </div>
                        <div class="mb-3">
                            <label for="situationFamiliale" class="form-label">Situation familiale</label>
                            <select class="form-select" id="situationFamiliale" name="situationFamiliale">
                                <option value="CELIBATAIRE">Célibataire</option>
                                <option value="MARIE">Marié(e)</option>
                                <option value="DIVORCE">Divorcé(e)</option>
                                <option value="VEUF">Veuf(ve)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="nombre_enfants" class="form-label">Nombre d'enfants</label>
                            <input type="number" class="form-control" id="nombre_enfants" name="nombre_enfants" min="0">
                        </div>
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="chef_famille" name="chef_famille">
                            <label class="form-check-label" for="chef_famille">Chef de famille</label>
                        </div>
                        <div class="mb-3">
                            <label for="enfant_charge" class="form-label">Enfant à charge</label>
                            <input type="number" class="form-control" id="enfant_charge" name="enfant_charge" min="0">
                        </div>
                        <div class="mb-3">
                            <label for="cnss" class="form-label">N° CNSS</label>
                            <input type="text" class="form-control" id="cnss" name="cnss">
                        </div>
                        <div class="mb-3">
                            <label for="nationalite" class="form-label">Nationalité</label>
                            <input type="text" class="form-control" id="nationalite" name="nationalite">
                        </div>
                        <div class="mb-3">
                            <label for="telephone" class="form-label">Téléphone</label>
                            <input type="text" class="form-control" id="telephone" name="telephone">
                        </div>
                        <div class="mb-3">
                            <label for="photo" class="form-label">Photo</label>
                            <input type="file" class="form-control" id="photo" name="photo" accept="image/*">
                        </div>
                        <div class="mb-3">
                            <label for="cv" class="form-label">CV (PDF, DOC, ...)</label>
                            <input type="file" class="form-control" id="cv" name="cv" accept=".pdf,.doc,.docx,.odt">
                        </div>
                    </div>
                    <!-- Colonne droite : Informations professionnelles -->
                    <div class="col-md-6">
                        <h5 class="mb-3 mt-2">Informations professionnelles</h5>
                        <div class="mb-3">
                            <label for="salaireBase" class="form-label">Salaire de base</label>
                            <input type="number" class="form-control" id="salaireBase" name="salaireBase" required>
                        </div>
                        <div class="mb-3">
                            <label for="regime_travail" class="form-label">Régime de travail</label>
                            <input type="text" class="form-control" id="regime_travail" name="regime_travail" placeholder="heures / semaine">
                        </div>
                        <div class="mb-3">
                            <label for="categorie" class="form-label">Catégorie</label>
                            <input type="text" class="form-control" id="categorie" name="categorie">
                        </div>
                        <div class="mb-3">
                            <label for="conge_mensuel" class="form-label">Congé mensuel</label>
                            <input type="number" class="form-control" id="conge_mensuel" name="conge_mensuel" min="0">
                        </div>
                        <div class="mb-3">
                            <label for="solde_conge" class="form-label">Solde congé</label>
                            <input type="number" class="form-control" id="solde_conge" name="solde_conge" min="0">
                        </div>
                        <div class="mb-3">
                            <label for="regime_salaire" class="form-label">Régime de salaire</label>
                            <select class="form-select" id="regime_salaire" name="regime_salaire">
                                <option value="MENSUEL">Mensuel</option>
                                <option value="HEBDOMADAIRE">Hebdomadaire</option>
                                <option value="JOURNALIER">Journalier</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="qualification" class="form-label">Qualification</label>
                            <input type="text" class="form-control" id="qualification" name="qualification">
                        </div>
                        <div class="mb-3">
                            <label for="dateEmbauche" class="form-label">Date de recrutement</label>
                            <input type="date" class="form-control" id="dateEmbauche" name="dateEmbauche" required>
                        </div>
                        <div class="mb-3">
                            <label for="typeContrat" class="form-label">Type de contrat</label>
                            <select class="form-select" id="typeContrat" name="typeContrat">
                                <option value="CDI">CDI</option>
                                <option value="CDD">CDD</option>
                                <option value="STAGE">Stage</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="duree_contrat" class="form-label">Durée de contrat (en mois)</label>
                            <input type="number" class="form-control" id="duree_contrat" name="duree_contrat" min="0" placeholder="Durée de contrat en mois">
                        </div>
                        <div class="mb-3">
                            <label for="fin_contrat" class="form-label">Fin contrat</label>
                            <input type="date" class="form-control" id="fin_contrat" name="fin_contrat">
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="appliquer_cnss" name="appliquer_cnss">
                            <label class="form-check-label" for="appliquer_cnss">Appliquer CNSS</label>
                        </div>
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="appliquer_irpp" name="appliquer_irpp">
                            <label class="form-check-label" for="appliquer_irpp">Appliquer IRPP</label>
                        </div>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">Ajouter</button>
                <a href="/sirh/employes/" class="btn btn-secondary ms-2">Annuler</a>
            </form>
            <div id="employe-add-success" class="alert alert-success" style="display:none;"></div>
        </div>
    </div>
</div>
<script src="{% static 'sirh/js/employes_add.js' %}"></script>
{% endblock %}
