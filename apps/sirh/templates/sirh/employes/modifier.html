{% extends "base/base.html" %}
{% load static %}

{% block content %}
<div class="main-wrapper">
    {% include 'includes/header.html' %}
    {% include 'includes/sidebar.html' %}
    <div class="page-wrapper">
        <div class="container mt-4">
            <h1 class="mb-4">Modifier un employé</h1>
            <div id="employe-edit-error" class="alert alert-danger" style="display:none;"></div>
            <form id="employe-edit-form" class="mb-4">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="nom" class="form-label">Nom</label>
                            <input type="text" class="form-control" id="nom" name="nom" required>
                        </div>
                        <div class="mb-3">
                            <label for="prenom" class="form-label">Prénom</label>
                            <input type="text" class="form-control" id="prenom" name="prenom" required>
                        </div>
                        <div class="mb-3">
                            <label for="fonction" class="form-label">Fonction</label>
                            <input type="text" class="form-control" id="fonction" name="fonction" required>
                        </div>
                        <div class="mb-3">
                            <label for="salaireBase" class="form-label">Salaire de base</label>
                            <input type="number" class="form-control" id="salaireBase" name="salaireBase" required>
                        </div>
                        <div class="mb-3">
                            <label for="dateEmbauche" class="form-label">Date d'embauche</label>
                            <input type="date" class="form-control" id="dateEmbauche" name="dateEmbauche" required>
                        </div>
                        <div class="mb-3">
                            <label for="personnesACharge" class="form-label">Personnes à charge</label>
                            <input type="number" class="form-control" id="personnesACharge" name="personnesACharge" value="0">
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email">
                        </div>
                        <div class="mb-3">
                            <label for="telephone" class="form-label">Téléphone</label>
                            <input type="text" class="form-control" id="telephone" name="telephone">
                        </div>
                        <div class="mb-3">
                            <label for="typeContrat" class="form-label">Type de contrat</label>
                            <select class="form-select" id="typeContrat" name="typeContrat">
                                <option value="CDI">CDI</option>
                                <option value="CDD">CDD</option>
                                <option value="STAGE">Stage</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="departement" class="form-label">Département</label>
                            <input type="text" class="form-control" id="departement" name="departement">
                        </div>
                        <div class="mb-3">
                            <label for="situationFamiliale" class="form-label">Situation familiale</label>
                            <select class="form-select" id="situationFamiliale" name="situationFamiliale">
                                <option value="CELIBATAIRE">Célibataire</option>
                                <option value="MARIE">Marié(e)</option>
                                <option value="DIVORCE">Divorcé(e)</option>
                                <option value="VEUF">Veuf(ve)</option>
                            </select>
                        </div>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">Enregistrer</button>
                <a href="/sirh/employes/" class="btn btn-secondary ms-2">Annuler</a>
            </form>
            <div id="employe-edit-success" class="alert alert-success" style="display:none;"></div>
        </div>
    </div>
</div>
<script src="{% static 'sirh/js/employes_edit.js' %}"></script>
{% endblock %}
