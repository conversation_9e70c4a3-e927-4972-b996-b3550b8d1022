{% extends "base/base.html" %}
{% load static %}

{% block content %}
<div class="main-wrapper">
    {% include 'includes/header.html' %}
    {% include 'includes/sidebar.html' %}
    <div class="page-wrapper">
        <div class="container mt-4">
            <h1 class="mb-4">Liste des employés</h1>
            <div id="employes-loader" style="display:none;">Chargement...</div>
            <div id="employes-error" class="alert alert-danger" style="display:none;"></div>
            <table id="employes-table" class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nom</th>
                        <th>Prénom</th>
                        <th>Email</th>
                        <th>Poste</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Rempli dynamiquement par JS -->
                </tbody>
            </table>
            <a href="/sirh/employes/ajouter/" class="btn btn-success">Ajouter un employé</a>
        </div>
    </div>
</div>
<script src="{% static 'sirh/js/employes.js' %}"></script>
<script>
// Ajoute le bouton Bulletins dans la colonne Actions via JS
document.addEventListener('DOMContentLoaded', function () {
    const table = document.getElementById('employes-table');
    if (!table) return;
    // On suppose que le JS principal remplit déjà les lignes, on monkey-patch la fonction de rendu si besoin
    if (window.renderEmployeRow) {
        const oldRender = window.renderEmployeRow;
        window.renderEmployeRow = function(employe) {
            const tr = oldRender(employe);
            const actionsTd = tr.querySelector('td.actions');
            if (actionsTd) {
                const bulletinsBtn = document.createElement('a');
                bulletinsBtn.className = 'btn btn-sm btn-info ms-1';
                bulletinsBtn.textContent = 'Bulletins';
                bulletinsBtn.href = `/sirh/employes/${employe.id}/bulletins/`;
                bulletinsBtn.title = 'Voir les bulletins de paie';
                actionsTd.appendChild(bulletinsBtn);
            }
            return tr;
        }
    }
});
</script>
{% endblock %}
