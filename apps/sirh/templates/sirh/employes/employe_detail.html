{% extends 'base/base.html' %}
{% block content %}
<div class="main-wrapper">
    {% include 'includes/header.html' %}
    {% include 'includes/sidebar.html' %}
    <div class="page-wrapper">
        <div class="container mt-4">
            <h1>Détail de l'employé</h1>
            <div id="employe-detail-loader" style="display:none;">Chargement...</div>
            <div id="employe-detail-error" class="alert alert-danger" style="display:none;"></div>
            <div id="employe-detail-content" style="display:none;">
                <!-- Les infos employé seront injectées ici par JS -->
            </div>
            <div class="mt-4">
                <a id="btn-bulletins" href="#" class="btn btn-outline-primary">
                    <i class="bi bi-file-earmark-text"></i> Voir les bulletins de paie
                </a>
            </div>
        </div>
    </div>
</div>
<script>
// Exemple de chargement AJAX du détail employé
function fetchEmployeDetail(employeId) {
    console.log('fetchEmployeDetail appelé', employeId);
    document.getElementById('employe-detail-loader').style.display = '';
    document.getElementById('employe-detail-error').style.display = 'none';
    document.getElementById('employe-detail-content').style.display = 'none';
    // Met à jour le lien bulletins
    const bulletinsBtn = document.getElementById('btn-bulletins');
    if (bulletinsBtn) {
        bulletinsBtn.href = `/sirh/employes/${employeId}/bulletins/`;
    }
    fetch(`/sirh/api/employes/${employeId}/`)
        .then(async r => {
            let json = null;
            try { json = await r.clone().json(); } catch(e) {}
            console.log('Réponse brute employé:', r, json);
            if (!r.ok) {
                let msg = `Erreur API: ${r.status} ${r.statusText}`;
                if (json && json.error) msg += `\n${json.error}`;
                msg += `\nRéponse brute: ` + JSON.stringify(json);
                throw new Error(msg);
            }
            return json;
        })
        .then(data => {
            document.getElementById('employe-detail-loader').style.display = 'none';
            console.log('Détail employé reçu:', data);
            if (data.error) {
                document.getElementById('employe-detail-error').innerText = data.error + '\n' + JSON.stringify(data);
                document.getElementById('employe-detail-error').style.display = '';
                return;
            }
            // Correction : si data.data existe, on l'utilise (structure API SIRH)
            let employe = (data && typeof data === 'object' && data.data) ? data.data : data;
            const labels = {
                nom: 'Nom',
                prenom: 'Prénom',
                fonction: 'Fonction',
                salaireBase: 'Salaire de base',
                dateEmbauche: "Date d'embauche",
                personnesACharge: 'Personnes à charge',
                actif: 'Actif',
                dateNaissance: 'Date de naissance',
                lieuNaissance: 'Lieu de naissance',
                nationalite: 'Nationalité',
                telephone: 'Téléphone',
                email: 'Email',
                adresseComplete: 'Adresse',
                typeContrat: 'Type de contrat',
                dateFinContrat: 'Date fin contrat',
                departement: 'Département',
                managerId: 'Manager',
                numeroSecuriteSociale: 'N° Sécurité Sociale',
                situationFamiliale: 'Situation familiale',
                regimeFiscal: 'Régime fiscal',
                iban: 'IBAN',
                bic: 'BIC',
                banque: 'Banque',
                statutEmploye: 'Statut',
                notesRh: 'Notes RH',
                createdAt: 'Créé le',
                updatedAt: 'Modifié le',
            };
            let html = `<div class='row'><div class='col-md-8'><table class='table table-bordered'>`;
            let found = false;
            for (const [key, label] of Object.entries(labels)) {
                if (employe[key] !== undefined && employe[key] !== null) {
                    found = true;
                    let value = employe[key];
                    if (typeof value === 'boolean') value = value ? 'Oui' : 'Non';
                    if (key === 'salaireBase') value = value + ' FCFA';
                    if (key === 'dateEmbauche' || key === 'createdAt' || key === 'updatedAt' || key === 'dateFinContrat' || key === 'dateNaissance') {
                        value = value ? new Date(value).toLocaleDateString('fr-FR') : '';
                    }
                    html += `<tr><th>${label}</th><td>${value}</td></tr>`;
                }
            }
            html += `</table></div></div>`;
            if (!found) {
                html += `<div class='alert alert-warning mt-3'>Aucune donnée employé reconnue.<br>JSON brut : <pre>${JSON.stringify(data, null, 2)}</pre></div>`;
            }
            document.getElementById('employe-detail-content').innerHTML = html;
            document.getElementById('employe-detail-content').style.display = '';
        })
        .catch(e => {
            document.getElementById('employe-detail-loader').style.display = 'none';
            document.getElementById('employe-detail-error').innerText = 'Erreur de chargement : ' + e.message;
            document.getElementById('employe-detail-error').style.display = '';
        });
}
// Appel automatique si employeId dans l'URL (à adapter selon le routage)
document.addEventListener('DOMContentLoaded', function() {
    const match = window.location.pathname.match(/employes\/([\w-]+)/);
    if (match) fetchEmployeDetail(match[1]);
});
</script>
{% endblock %}
