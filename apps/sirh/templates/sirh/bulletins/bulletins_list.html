{% extends 'base/base.html' %}
{% load static %}

{% block content %}
<div class="main-wrapper">
    {% include 'includes/header.html' %}
    {% include 'includes/sidebar.html' %}
    <div class="page-wrapper">
        <div class="container mt-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="mb-0">Bulletins de paie de l'employé</h1>
                <div class="d-flex align-items-center gap-2">
                    <button id="btn-generer-bulletin" class="btn btn-success">
                        <i class="bi bi-plus-circle"></i> Générer un bulletin
                    </button>
                    <a href="#" class="btn btn-outline-primary" id="btn-telecharger-bulletins-employe">
                        <i class="bi bi-download"></i> Télécharger tout (PDF)
                    </a>
                    <form id="form-telecharger-bulletin-periode" class="d-flex align-items-center ms-2" style="gap: 0.5rem;">
                        <input type="month" class="form-control form-control-sm" id="periode-bulletin" required>
                        <button type="submit" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-file-earmark-pdf"></i> Télécharger ce mois
                        </button>
                    </form>
                </div>
            </div>
            <form id="bulletins-filters" class="row g-3 mb-3">
                <div class="col-md-3">
                    <input type="month" class="form-control" id="filter-periode" placeholder="Période">
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filter-statut">
                        <option value="">Tous statuts</option>
                        <option value="VALIDE">Validé</option>
                        <option value="BROUILLON">Brouillon</option>
                        <option value="ANNULE">Annulé</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary">Filtrer</button>
                </div>
            </form>
            <div id="bulletins-error" class="alert alert-danger d-none"></div>
            <div class="card">
                <div class="card-body">
                    <table class="table table-striped" id="bulletins-table">
                        <thead>
                            <tr>
                                <th scope="col">#</th>
                                <th scope="col">Période</th>
                                <th scope="col">Net à payer</th>
                                <th scope="col">Statut</th>
                                <th scope="col">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Les bulletins seront injectés ici par JS -->
                        </tbody>
                    </table>
                    <div id="no-bulletins" class="text-center text-muted d-none">Aucun bulletin trouvé pour cet employé.</div>
                </div>
            </div>
            <a href="{# employes list url #}" class="btn btn-secondary mt-3">Retour à la liste des employés</a>
        </div>
    </div>
</div>
<script src="{% static 'sirh/js/bulletins_list.js' %}"></script>
<script>
// Bouton téléchargement PDF pour tous les bulletins de l'employé
document.addEventListener('DOMContentLoaded', function () {
    const btn = document.getElementById('btn-telecharger-bulletins-employe');
    if (btn) {
        // Récupérer l'ID employé depuis l'URL
        const match = window.location.pathname.match(/employes\/(.+?)\/bulletins/);
        if (match) {
            const employeId = match[1];
            btn.href = `/sirh/api/employes/${employeId}/bulletins/?format=pdf`;
            btn.target = '_blank';
        } else {
            btn.style.display = 'none';
        }
    }

    // Bouton pour télécharger le bulletin d'un mois donné (workflow : fetch bulletin, puis fetch PDF)
    const formPeriode = document.getElementById('form-telecharger-bulletin-periode');
    if (formPeriode) {
        formPeriode.addEventListener('submit', async function (e) {
            e.preventDefault();
            const input = document.getElementById('periode-bulletin');
            const periode = input.value;
            const match = window.location.pathname.match(/employes\/(.+?)\/bulletins/);
            if (periode && match) {
                const employeId = match[1];
                try {
                    // 1. Récupérer le bulletin pour la période
                    const url = `/sirh/api/employes/${employeId}/bulletins/?periode=${periode}`;
                    const resp = await fetch(url, { credentials: 'same-origin', headers: { 'X-Requested-With': 'XMLHttpRequest' } });
                    const data = await resp.json();
                    let bulletin = null;
                    if (Array.isArray(data.data)) {
                        bulletin = data.data[0];
                    } else if (Array.isArray(data)) {
                        bulletin = data[0];
                    } else if (data && data.id) {
                        bulletin = data;
                    }
                    if (bulletin && bulletin.id) {
                        // 2. Ouvrir le PDF
                        window.open(`/sirh/api/paie/bulletins/${bulletin.id}/pdf`, '_blank');
                    } else {
                        alert('Aucun bulletin trouvé pour cette période.');
                    }
                } catch (err) {
                    alert('Erreur lors de la récupération du bulletin.');
                }
            }
        });
    }
});
</script>

{% endblock %}

{% block extra_js %}
<script src="{% static 'sirh/js/bulletins_list.js' %}"></script>
{% endblock %}
