from django.urls import path
from django.shortcuts import render

from .views import (
    EmployesListApiView,
    BulletinsListView,
    PretsListView,
    PointageListView,
    AbsencesListView,
    HeuresListView,
    SanteListView,
    EmployeDetailView,
    EmployeDetailApiView,
    EntreprisesListApiView,
    EmployeBulletinsApiView,
    GenererBulletinApiView,
    BulletinsGlobalApiView,
    BulletinPdfProxyView,
)

app_name = "sirh"

urlpatterns = [
    path('api/paie/bulletins/<uuid:bulletin_id>/pdf', BulletinPdfProxyView.as_view(), name='sirh_api_bulletin_pdf'),
    path('api/paie/bulletins/', BulletinsGlobalApiView.as_view(), name='sirh_api_bulletins_global'),
    path('api/entreprises/', EntreprisesListApiView.as_view(), name='sirh_api_entreprises'),
    path('api/employes/', EmployesListApiView.as_view(), name='sirh_api_employes'),
    path('api/employes/<str:employe_id>/', EmployeDetailApiView.as_view(), name='sirh_api_employe_detail'),
    path('api/employes/<str:employe_id>/bulletins/', EmployeBulletinsApiView.as_view(), name='sirh_api_employe_bulletins'),
    path('api/employes/<str:employe_id>/generer-bulletin/', GenererBulletinApiView.as_view(), name='sirh_api_generer_bulletin'),
    path('employes/', lambda request: render(request, 'sirh/employes/employes_list.html'), name='employe_list'),
    path('employes/ajouter/', lambda request: render(request, 'sirh/employes/ajouter.html'), name='employe_add'),
    path('employes/<str:employe_id>/modifier/', lambda request, employe_id: render(request, 'sirh/employes/modifier.html', {'employe_id': employe_id}), name='employe_edit'),
    path('employes/<str:employe_id>/', EmployeDetailView.as_view(), name='employe_detail'),
    path('employes/<str:employe_id>/bulletins/', BulletinsListView.as_view(), name='sirh_bulletins_list'),
    path('bulletins/', BulletinsListView.as_view(), name='bulletin_list'),
    path('prets/', PretsListView.as_view(), name='pret_list'),
    path('pointage/', PointageListView.as_view(), name='pointage_list'),
    path('absences/', AbsencesListView.as_view(), name='absence_list'),
    path('heures/', HeuresListView.as_view(), name='heures_list'),
    path('sante/', SanteListView.as_view(), name='sante_list'),
]
 