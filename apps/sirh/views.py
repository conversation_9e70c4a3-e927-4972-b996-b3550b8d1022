from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views.generic import TemplateView
from django.views import View
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from .services.sirh_api import SirhApiService
from django.http import JsonResponse, HttpResponse

# Proxy PDF bulletin
@method_decorator(csrf_exempt, name='dispatch')
class BulletinPdfProxyView(LoginRequiredMixin, View):
    def get(self, request, bulletin_id):
        service = SirhApiService()
        try:
            pdf_content = service.export_bulletin_pdf(bulletin_id)
            response = HttpResponse(pdf_content, content_type='application/pdf')
            response['Content-Disposition'] = f'inline; filename=bulletin_{bulletin_id}.pdf'
            return response
        except Exception as e:
            import traceback
            tb = traceback.format_exc()
            return JsonResponse({'error': str(e), 'traceback': tb}, status=500)
from .services.sirh_api import SirhApiService
from django.http import JsonResponse

@method_decorator(csrf_exempt, name='dispatch')
class BulletinsGlobalApiView(LoginRequiredMixin, View):
    def get(self, request):
        service = SirhApiService()
        try:
            params = request.GET.dict()
            data = service.get_bulletins(params=params)
            return JsonResponse(data, safe=False)
        except Exception as e:
            import traceback
            tb = traceback.format_exc()
            return JsonResponse({'error': str(e), 'traceback': tb}, status=500)

@method_decorator(csrf_exempt, name='dispatch')
class EmployesListApiView(LoginRequiredMixin, View):
    def get(self, request):
        service = SirhApiService()
        try:
            data = service.get_employes()
            return JsonResponse(data, safe=False)
        except Exception as e:
            import traceback
            tb = traceback.format_exc()
            return JsonResponse({'error': str(e), 'traceback': tb}, status=500)

    def post(self, request):
        import json, traceback
        service = SirhApiService()
        try:
            payload = json.loads(request.body.decode('utf-8'))
            data = service.create_employe(payload)
            return JsonResponse(data, safe=False, status=201)
        except Exception as e:
            tb = traceback.format_exc()
            return JsonResponse({'error': str(e), 'traceback': tb}, status=500)

# Détail employé
class EmployeDetailView(LoginRequiredMixin, TemplateView):
    template_name = 'sirh/employes/employe_detail.html'

# Proxy API pour détail employé SIRH
@method_decorator(csrf_exempt, name='dispatch')
class EmployeDetailApiView(LoginRequiredMixin, View):
    def get(self, request, employe_id):
        import traceback
        service = SirhApiService()
        try:
            data = service.get_employe(employe_id)
            return JsonResponse(data, safe=False)
        except Exception as e:
            tb = traceback.format_exc()
            return JsonResponse({'error': str(e), 'traceback': tb}, status=500)

    def put(self, request, employe_id):
        import json, traceback
        service = SirhApiService()
        try:
            payload = json.loads(request.body.decode('utf-8'))
            data = service.update_employe(employe_id, payload)
            return JsonResponse(data, safe=False)
        except Exception as e:
            tb = traceback.format_exc()
            return JsonResponse({'error': str(e), 'traceback': tb}, status=500)

@method_decorator(csrf_exempt, name='dispatch')
class EntreprisesListApiView(LoginRequiredMixin, View):
    def get(self, request):
        import traceback
        service = SirhApiService()
        try:
            data = service.get_entreprises()
            return JsonResponse(data, safe=False)
        except Exception as e:
            tb = traceback.format_exc()
            return JsonResponse({'error': str(e), 'traceback': tb}, status=500)

class EmployesListApiView(LoginRequiredMixin, View):
    def get(self, request):
        import traceback
        service = SirhApiService()
        try:
            employes = service.get_employes()
            return JsonResponse(employes, safe=False)
        except Exception as e:
            tb = traceback.format_exc()
            return JsonResponse({'error': str(e), 'traceback': tb}, status=500)


# Bulletins de paie
@method_decorator(csrf_exempt, name='dispatch')
class EmployeBulletinsApiView(LoginRequiredMixin, View):
    def get(self, request, employe_id):
        import traceback
        service = SirhApiService()
        try:
            data = service.get_bulletins_employe(employe_id)
            return JsonResponse(data, safe=False)
        except Exception as e:
            tb = traceback.format_exc()
            return JsonResponse({'error': str(e), 'traceback': tb}, status=500)

# Générer un bulletin pour un employé
@method_decorator(csrf_exempt, name='dispatch')
class GenererBulletinApiView(LoginRequiredMixin, View):
    def post(self, request, employe_id):
        import json, traceback
        service = SirhApiService()
        try:
            payload = json.loads(request.body.decode('utf-8'))
            data = service.generer_bulletin_employe(employe_id, payload)
            return JsonResponse(data, safe=False, status=201)
        except Exception as e:
            tb = traceback.format_exc()
            return JsonResponse({'error': str(e), 'traceback': tb}, status=500)

# Bulletins de paie
class BulletinsListView(LoginRequiredMixin, TemplateView):
    def get_template_names(self):
        # Si on est sur la route employé, on affiche bulletins_list.html, sinon all_bulletins.html
        employe_id = self.kwargs.get('employe_id', None)
        if employe_id:
            return ['sirh/bulletins/bulletins_list.html']
        return ['sirh/bulletins/all_bulletins.html']

# Prêts / Avances
class PretsListView(LoginRequiredMixin, TemplateView):
    template_name = 'sirh/prets/prets_list.html'

# Pointage
class PointageListView(LoginRequiredMixin, TemplateView):
    template_name = 'sirh/pointage/pointage_list.html'

# Absences
class AbsencesListView(LoginRequiredMixin, TemplateView):
    template_name = 'sirh/absences/absences_list.html'

# Heures
class HeuresListView(LoginRequiredMixin, TemplateView):
    template_name = 'sirh/heures/heures_list.html'

# Paramètres santé
class SanteListView(LoginRequiredMixin, TemplateView):
    template_name = 'sirh/sante/sante_list.html'
