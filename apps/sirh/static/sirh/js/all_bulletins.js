// static/sirh/js/all_bulletins.js
// Liste globale des bulletins de paie avec filtres

document.addEventListener('DOMContentLoaded', function () {
    const tableBody = document.querySelector('#all-bulletins-table tbody');
    const errorDiv = document.getElementById('bulletins-error');
    const noBulletins = document.getElementById('no-bulletins');
    const form = document.getElementById('bulletins-filters');

    function fetchAllBulletins(params = {}) {
        errorDiv.classList.add('d-none');
        let url = '/sirh/api/paie/bulletins/?';
        if (params.employeId) url += `employeId=${encodeURIComponent(params.employeId)}&`;
        if (params.periode) url += `periode=${encodeURIComponent(params.periode)}&`;
        if (params.statut) url += `statut=${encodeURIComponent(params.statut)}&`;
        url += 'limit=50';
        fetch(url, {
            credentials: 'same-origin',
            headers: { 'X-Requested-With': 'XMLHttpRequest' },
        })
            .then(r => r.json())
            .then(data => {
                let bulletins = Array.isArray(data.data) ? data.data : data;
                tableBody.innerHTML = '';
                if (!bulletins || bulletins.length === 0) {
                    noBulletins.classList.remove('d-none');
                    return;
                }
                noBulletins.classList.add('d-none');
                bulletins.forEach((b, idx) => {
                    let periode = b.periode && b.periode.length >= 7 ? b.periode.slice(0, 7) : '';
                    const net = b.netAPayer || b.net_a_payer || '';
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                        <th scope="row">${idx + 1}</th>
                        <td>${b.employeId || ''}</td>
                        <td>${periode}</td>
                        <td>${net}</td>
                        <td>${b.statut || ''}</td>
                        <td>
                            <a href="/sirh/paie/bulletins/${b.id}/" class="btn btn-sm btn-outline-primary">Voir</a>
                            ${b.pdf_url ? `<a href="${b.pdf_url}" class="btn btn-sm btn-primary ms-1" target="_blank">PDF</a>` : ''}
                        </td>
                    `;
                    tableBody.appendChild(tr);
                });
            })
            .catch(err => {
                errorDiv.textContent = err.message || 'Erreur lors du chargement des bulletins.';
                errorDiv.classList.remove('d-none');
            });
    }

    form.addEventListener('submit', function (e) {
        e.preventDefault();
        fetchAllBulletins({
            employeId: document.getElementById('filter-employe').value,
            periode: document.getElementById('filter-periode').value,
            statut: document.getElementById('filter-statut').value
        });
    });

    // Chargement initial
    fetchAllBulletins();
});
