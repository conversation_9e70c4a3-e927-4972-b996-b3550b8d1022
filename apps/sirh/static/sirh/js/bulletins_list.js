// static/sirh/js/bulletins_list.js
// Affiche la liste des bulletins de paie pour un employé donné

document.addEventListener('DOMContentLoaded', function () {
  // Récupère l'ID de l'employé depuis l'URL (ex: /sirh/employes/uuid/bulletins/)
  const match = window.location.pathname.match(/employes\/([\w-]+)\/bulletins/);
  const employeId = match ? match[1] : null;
  if (!employeId) {
    showError("Impossible de déterminer l'employé.");
    return;
  }
  // Gestion des filtres
  const filtersForm = document.getElementById("bulletins-filters");
  if (filtersForm) {
    filtersForm.addEventListener("submit", function (e) {
      e.preventDefault();
      fetchBulletins(employeId, {
        periode: document.getElementById("filter-periode").value,
        statut: document.getElementById("filter-statut").value,
      });
    });
  }
  fetchBulletins(employeId);

  // Gestion du bouton Générer un bulletin
  const btnGenerer = document.getElementById("btn-generer-bulletin");
  if (btnGenerer) {
    btnGenerer.addEventListener("click", function () {
      genererBulletin(employeId);
    });
  }
});

function genererBulletin(employeId) {
    // Demande la période à l'utilisateur
    const periode = prompt('Période du bulletin (ex: 2024-01) :');
    if (!periode) return;
    // Demande le sursalaire (optionnel)
    const sursalaire = prompt('Sursalaire (optionnel, valeur numérique) :', '0');
    // Demande logement/transport (optionnel)
    const logementTransport = prompt('Logement/Transport (optionnel, valeur numérique) :', '0');
    const body = {
        periode: periode,
        sursalaire: Number(sursalaire) || 0,
        logementTransport: Number(logementTransport) || 0
    };
    fetch(`/sirh/api/employes/${employeId}/generer-bulletin/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'same-origin',
        body: JSON.stringify(body)
    })
        .then(response => response.json().then(data => ({ status: response.status, data })))
        .then(({ status, data }) => {
            if (status !== 200 && status !== 201) {
                showError(data.error || 'Erreur lors de la génération du bulletin.');
                if (data.traceback) console.error(data.traceback);
                return;
            }
            alert('Bulletin généré avec succès.');
            fetchBulletins(employeId);
        })
        .catch(err => {
            showError('Erreur réseau lors de la génération du bulletin.');
            console.error(err);
        });
}

function fetchBulletins(employeId, filters = {}) {
  let url = `/sirh/api/employes/${employeId}/bulletins/?`;
  if (filters.periode) url += `periode=${encodeURIComponent(filters.periode)}&`;
  if (filters.statut) url += `statut=${encodeURIComponent(filters.statut)}&`;
  fetch(url, {
    credentials: "same-origin",
    headers: {
      "X-Requested-With": "XMLHttpRequest",
    },
  })
    .then((response) =>
      response.json().then((data) => ({ status: response.status, data }))
    )
    .then(({ status, data }) => {
      if (status !== 200) {
        showError(data.error || "Erreur lors du chargement des bulletins.");
        if (data.traceback) console.error(data.traceback);
        return;
      }
      // Si la réponse a une clé "data" (API SIRH), on l'utilise
      const bulletins = Array.isArray(data.data) ? data.data : data;
      renderBulletins(bulletins);
    })
    .catch((err) => {
      showError("Erreur réseau lors du chargement des bulletins.");
      console.error(err);
    });
}

function renderBulletins(bulletins) {
    const tbody = document.querySelector('#bulletins-table tbody');
    const noBulletins = document.getElementById('no-bulletins');
    tbody.innerHTML = '';
    if (!bulletins || !Array.isArray(bulletins) || bulletins.length === 0) {
        noBulletins.classList.remove('d-none');
        return;
    }
    noBulletins.classList.add('d-none');
    bulletins.forEach((b, idx) => {
        const tr = document.createElement('tr');
        // Format période (YYYY-MM)
        let periode = b.periode;
        if (periode && periode.length >= 7) {
            periode = periode.slice(0, 7);
        }
        // net à payer : netAPayer ou net_a_payer
        const net = b.netAPayer || b.net_a_payer || '';
        let pdfUrl = b.pdf_url || `/sirh/api/paie/bulletins/${b.id}/pdf`;
        tr.innerHTML = `
            <th scope="row">${idx + 1}</th>
            <td>${periode || ""}</td>
            <td>${net}</td>
            <td>${b.statut || ""}</td>
            <td>
                <a href="${pdfUrl}" class="btn btn-sm btn-primary" target="_blank">PDF</a>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

function showError(msg) {
    const errorDiv = document.getElementById('bulletins-error');
    errorDiv.textContent = msg;
    errorDiv.classList.remove('d-none');
}
