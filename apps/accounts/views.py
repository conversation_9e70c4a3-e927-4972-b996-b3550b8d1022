from django.shortcuts import render
from django.contrib.auth.views import LoginView, LogoutView
from django.contrib.auth import logout
from django.contrib import messages
from django.http import HttpResponseRedirect
from django.urls import reverse_lazy, reverse
from django.core.exceptions import PermissionDenied
from apps.accounts.permissions import can_access_section
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_protect
from django.contrib.auth.decorators import login_required

from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, permission_required
from .decorators import admin_only, require_permission, manager_or_admin
from django.contrib import messages
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.db.models import Count
from .models import VendeurProfile
# from .models import ComptableProfile, CaissierProfile, MagasinierProfile
from rolepermissions.roles import assign_role
from django.contrib.auth import get_user_model
from apps.accounts.forms import RoleForm, PERMISSIONS_FR
from .permissions import ROLE_PERMISSIONS

User = get_user_model()


class CustomLoginView(LoginView):
    template_name = 'pages/sign-in.html'  # Ton template de connexion

    def get_success_url(self):
        user = self.request.user
        if hasattr(user, 'role') and user.role == 'SELLER':
            return '/sales/sale/'
        # Ajoute ici d'autres redirections selon le rôle si besoin
        return super().get_success_url()

@login_required
def user_logout(request):
    logout(request)
    messages.success(request, "Vous avez été déconnecté avec succès.")
    return redirect('accounts:logout_confirmation')

def logout_confirmation(request):
    return render(request, 'pages/logout_confirmation.html')


@login_required
def profile(request):
    """Vue pour la gestion du profil utilisateur"""
    if request.method == 'POST':
        user = request.user
        # Mise à jour des informations de base
        user.first_name = request.POST.get('first_name', user.first_name)
        user.last_name = request.POST.get('last_name', user.last_name)
        user.email = request.POST.get('email', user.email)
        user.phone = request.POST.get('phone', user.phone)
        user.address = request.POST.get('address', user.address)

        # Gestion du changement de mot de passe
        old_password = request.POST.get('old_password')
        new_password = request.POST.get('new_password')
        confirm_password = request.POST.get('confirm_password')

        if old_password and new_password and confirm_password:
            if not user.check_password(old_password):
                messages.error(request, "L'ancien mot de passe est incorrect.")
                return redirect('accounts:profile')
            
            if new_password != confirm_password:
                messages.error(request, "Les nouveaux mots de passe ne correspondent pas.")
                return redirect('accounts:profile')
            
            if len(new_password) < 8:
                messages.error(request, "Le nouveau mot de passe doit contenir au moins 8 caractères.")
                return redirect('accounts:profile')
            
            user.set_password(new_password)
            messages.success(request, "Mot de passe modifié avec succès.")

        user.save()
        messages.success(request, "Profil mis à jour avec succès.")
        return redirect('accounts:profile')

    return render(request, 'accounts/profile.html')


@login_required
@manager_or_admin
def role_list(request):
    if not can_access_section(request.user, 'users'):
        raise PermissionDenied
    roles = Group.objects.annotate(user_count=Count('user'))
    
    # Organiser les permissions par modèle
    content_types = ContentType.objects.all()
    permission_groups = []
    
    for ct in content_types:
        perms = Permission.objects.filter(content_type=ct)
        if perms.exists():
            permission_groups.append({
                'name': ct.model.title(),
                'permissions': perms
            })
    
    context = {
        'roles': roles,
        'permission_groups': permission_groups
    }
    return render(request, 'pages/roles_list.html', context)

@login_required
@manager_or_admin
def role_detail(request, pk):
    if not can_access_section(request.user, 'users'):
        raise PermissionDenied
    role = get_object_or_404(Group, pk=pk)
    
    # Organiser les permissions par modèle
    content_types = ContentType.objects.all()
    permission_groups = []
    
    for ct in content_types:
        perms = Permission.objects.filter(content_type=ct)
        if perms.exists():
            permission_groups.append({
                'name': ct.model.title(),
                'permissions': perms
            })
    
    context = {
        'role': role,
        'permission_groups': permission_groups
    }
    return render(request, 'pages/role_detail.html', context)

@login_required
@manager_or_admin
def add_role(request):
    if not can_access_section(request.user, 'users'):
        raise PermissionDenied
    if request.method == 'POST':
        form = RoleForm(request.POST)
        if form.is_valid():
            role = form.save()
            messages.success(request, f'Le rôle {role.name} a été créé avec succès.')
            return redirect('role_list')
    else:
        form = RoleForm()
    
    return render(request, 'users/role_form.html', {'form': form})

@login_required
@manager_or_admin
def edit_role(request, pk):
    if not can_access_section(request.user, 'users'):
        raise PermissionDenied
    pass

@login_required
@manager_or_admin
def delete_role(request, pk):
    if not can_access_section(request.user, 'users'):
        raise PermissionDenied
    role = get_object_or_404(Group, pk=pk)
    
    if role.name == 'Admin':
        messages.error(request, 'Le rôle Admin ne peut pas être supprimé.')
        return redirect('role_list')
    
    if request.method == 'POST':
        role_name = role.name
        role.delete()
        messages.success(request, f'Le rôle {role_name} a été supprimé avec succès.')
        return redirect('role_list')
    
    return render(request, 'pages/role_confirm_delete.html', {'role': role})

def user_add_role(request, role):
    if not can_access_section(request.user, 'users'):
        raise PermissionDenied
    # Permettre vendeur ou grh (hr)
    role = role.lower()
    if role not in ['vendeur', 'grh', 'hr']:
        return redirect('accounts:user_list')

    is_modal = request.GET.get('modal') == '1'

    if role == 'vendeur':
        from apps.cash.models import CashRegister
        from .forms import SellerForm
        caisses_disponibles = CashRegister.objects.filter(owner__isnull=True)
        if request.method == 'POST':
            form = SellerForm(request.POST)
            if form.is_valid():
                user = form.save(commit=False)
                user.role = 'SELLER'
                user.is_active = True
                password = form.cleaned_data.get('password') or request.POST.get('password')
                if password:
                    user.set_password(password)
                user.save()
                caisse_id = request.POST.get('caisse_id')
                if caisse_id:
                    caisse = CashRegister.objects.get(id=caisse_id)
                    caisse.owner = user
                    caisse.save()
                messages.success(request, "Vendeur créé avec succès.")
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    from django.http import JsonResponse
                    return JsonResponse({'success': True, 'message': "Vendeur créé avec succès."})
                return redirect('accounts:user_list')
        else:
            form = SellerForm()
        template = 'pages/user_form.html' if is_modal else 'pages/user_list.html'
        context = {'role': role, 'caisses_disponibles': caisses_disponibles, 'form': form}
        return render(request, template, context)
    else:
        # GRH/HR
        from .forms import HRForm
        if request.method == 'POST':
            form = HRForm(request.POST)
            if form.is_valid():
                user = form.save(commit=False)
                user.role = 'HR'
                user.is_active = True
                password = form.cleaned_data.get('password') or request.POST.get('password')
                if password:
                    user.set_password(password)
                user.save()
                messages.success(request, "Gestionnaire RH créé avec succès.")
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    from django.http import JsonResponse
                    return JsonResponse({'success': True, 'message': "Gestionnaire RH créé avec succès."})
                return redirect('accounts:user_list')
        else:
            form = HRForm()
        template = 'pages/user_form.html' if is_modal else 'pages/user_list.html'
        context = {'role': role, 'form': form}
        return render(request, template, context)

def user_list(request):
    # Exemple de restriction d'accès avec page 403
    from django.core.exceptions import PermissionDenied
    from apps.accounts.permissions import can_access_section
    if not can_access_section(request.user, 'users'):
        raise PermissionDenied
    from apps.cash.models import CashRegister
    from .forms import SellerForm, HRForm
    # Récupère le rôle sélectionné dans l'onglet (GET param)
    selected_role = request.GET.get('role', 'SELLER').upper()
    # Liste des rôles à afficher dans les onglets
    ROLES = [
        ('SELLER', 'Vendeurs'),
        ('HR', 'GRH'),
        # Ajoute d'autres rôles ici si besoin
    ]
    users = User.objects.filter(role=selected_role)
    caisses_disponibles = CashRegister.objects.filter(owner__isnull=True)
    # Formulaire selon le rôle sélectionné
    if selected_role == 'HR':
        form = HRForm()
    else:
        form = SellerForm()
    return render(request, 'pages/user_list.html', {
        'users': users,
        'caisses_disponibles': caisses_disponibles,
        'form': form,
        'PERMISSIONS_FR': PERMISSIONS_FR,
        'ALL_PERMISSIONS': ROLE_PERMISSIONS['ADMIN']['permissions'],
        'ROLES': ROLES,
        'selected_role': selected_role,
    })

class CustomLogoutView(LogoutView):
    """Vue de déconnexion personnalisée"""
    next_page = reverse_lazy('accounts:login')

    def dispatch(self, request, *args, **kwargs):
        # Ajouter un message de confirmation avant la déconnexion
        if request.user.is_authenticated:
            messages.success(request, f"Au revoir {request.user.get_full_name() or request.user.email} ! Vous avez été déconnecté avec succès.")
        return super().dispatch(request, *args, **kwargs)


@login_required
@csrf_protect
@require_POST
def user_logout(request):
    """
    Vue de déconnexion sécurisée avec protection CSRF
    Invalide complètement la session utilisateur
    """
    if request.user.is_authenticated:
        user_name = request.user.get_full_name() or request.user.email
        user_role = request.user.get_role_display() if hasattr(request.user, 'get_role_display') else request.user.role

        # Log de sécurité (optionnel)
        import logging
        logger = logging.getLogger('accounts.logout')
        logger.info(f"Déconnexion utilisateur: {user_name} ({user_role}) - IP: {request.META.get('REMOTE_ADDR')}")

        # Déconnexion avec nettoyage complet de la session
        logout(request)

        # Message de confirmation
        messages.success(request, f"Au revoir {user_name} ! Vous avez été déconnecté avec succès.")

    # Redirection vers la page de connexion
    return HttpResponseRedirect(reverse_lazy('accounts:login'))


@login_required
def logout_confirmation(request):
    """
    Page de confirmation de déconnexion (optionnelle)
    """
    if request.method == 'POST':
        return user_logout(request)

    context = {
        'user': request.user,
        'page_title': 'Confirmation de déconnexion'
    }
    return render(request, 'pages/logout_confirmation.html', context)

def user_edit(request, user_id):
    from apps.cash.models import CashRegister
    user = get_object_or_404(User, id=user_id)
    if request.method == 'POST':
        email = request.POST.get('email')
        first_name = request.POST.get('first_name')
        last_name = request.POST.get('last_name')
        phone = request.POST.get('phone')
        address = request.POST.get('address')
        role = request.POST.get('role')
        is_active = request.POST.get('is_active') == 'on'
        caisse_id = request.POST.get('caisse_id')
        custom_permissions = request.POST.getlist('custom_permissions')

        # Empêcher de changer le rôle d'un vendeur
        if user.role == 'SELLER' and role != 'SELLER':
            messages.error(request, "Vous ne pouvez pas changer le rôle d'un vendeur.")
            return redirect('accounts:user_list')

        # Mise à jour des champs
        user.email = email
        user.first_name = first_name
        user.last_name = last_name
        user.phone = phone
        user.address = address
        user.role = role
        user.is_active = is_active
        
        # Mise à jour des permissions personnalisées
        user.custom_permissions = custom_permissions

        # Gestion de la caisse
        if caisse_id:
            # Désassigner l'ancienne caisse si elle existe
            CashRegister.objects.filter(owner=user).update(owner=None)
            
            # Assigner la nouvelle caisse
            caisse = CashRegister.objects.get(id=caisse_id)
            if caisse.owner and caisse.owner != user:
                messages.warning(request, f"La caisse était déjà assignée à {caisse.owner.get_full_name()}. Elle a été réassignée à {user.get_full_name()}.")
            caisse.owner = user
            caisse.save()
        else:
            # Si aucune caisse n'est sélectionnée, on désassigne l'ancienne
            CashRegister.objects.filter(owner=user).update(owner=None)
        
        user.save()
        messages.success(request, "Vendeur modifié avec succès.")
        return redirect('accounts:user_list')

    # Pour l'affichage du formulaire
    caisses_disponibles = list(CashRegister.objects.filter(owner__isnull=True))
    # Ajouter la caisse actuellement assignée à l'utilisateur si elle existe
    caisse_actuelle = CashRegister.objects.filter(owner=user).first()
    if caisse_actuelle and caisse_actuelle not in caisses_disponibles:
        caisses_disponibles.append(caisse_actuelle)

    return render(request, 'pages/user_list.html', {
        'users': User.objects.filter(role='SELLER'),
        'caisses_disponibles': caisses_disponibles,
    })

@login_required
@manager_or_admin
def user_delete(request, user_id):
    user = get_object_or_404(User, id=user_id)
    
    # Empêcher la suppression de l'utilisateur connecté
    if user == request.user:
        messages.error(request, "Vous ne pouvez pas supprimer votre propre compte.")
        return redirect('accounts:user_list')
    
    # Empêcher la suppression d'un administrateur
    if user.role == 'ADMIN':
        messages.error(request, "Vous ne pouvez pas supprimer un administrateur.")
        return redirect('accounts:user_list')
    
    if request.method == 'POST':
        user_name = user.get_full_name()
        # Les ventes resteront, mais sans vendeur associé
        user.delete()
        messages.success(request, f"Le vendeur {user_name} a été supprimé. Ses ventes restent dans l’historique, mais sans vendeur associé.")
        return redirect('accounts:user_list')
    
    return redirect('accounts:user_list')
