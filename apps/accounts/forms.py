from django import forms
from .models import User, Role
from .permissions import ROLE_PERMISSIONS

class RoleForm(forms.ModelForm):
    class Meta:
        model = Role
        fields = ['name', 'description', 'permissions']
        widgets = {
            'permissions': forms.CheckboxSelectMultiple
        }

# Noms descriptifs en français pour chaque permission
PERMISSIONS_FR = {
    'dashboard': 'Tableau de bord',
    'clients': 'Gestion des clients',
    'grossistes': 'Gestion des grossistes', 
    'deposits': 'Gestion des dépôts',
    'suppliers': 'Gestion des fournisseurs',
    'shops': 'Gestion des magasins',
    'products': 'Gestion des produits',
    'categories': 'Gestion des catégories',
    'expired_products': 'Produits expirés',
    'out_of_stock': 'Rupture de stock',
    'stock_reports': 'Rapports de stock',
    'sales': 'Gestion des ventes',
    'invoice_templates': 'Modèles de factures',
    'returns': 'Gestion des retours',
    'purchases': 'Gestion des achats',
    'purchase_invoices': 'Factures d\'achat',
    'expenses': 'Gestion des dépenses',
    'bank_accounts': 'Comptes bancaires',
    'cash_register': 'Caisse enregistreuse',
    'users': 'Gestion des utilisateurs',
    'reports': 'Rapports'
}

class SellerForm(forms.ModelForm):
    # Champ de mot de passe
    password = forms.CharField(
        label="Mot de passe",
        widget=forms.PasswordInput(),
        required=True,
        help_text="Mot de passe pour le compte vendeur"
    )
    confirm_password = forms.CharField(
        label="Confirmer le mot de passe",
        widget=forms.PasswordInput(),
        required=True
    )

    # Toutes les permissions disponibles (prendre celles de l'admin)
    ALL_PERMISSIONS = set(ROLE_PERMISSIONS['ADMIN']['permissions'])
    
    # Générer les choix avec les traductions françaises
    PERMISSION_CHOICES = [
        (perm, PERMISSIONS_FR.get(perm, perm))
        for perm in sorted(ALL_PERMISSIONS)
    ]
    
    # Champ de permissions avec toutes les options possibles
    custom_permissions = forms.MultipleChoiceField(
        choices=PERMISSION_CHOICES,
        widget=forms.CheckboxSelectMultiple,
        required=False,
        label="Permissions d'accès",
        initial=ROLE_PERMISSIONS['SELLER']['permissions']  # Pré-cocher les permissions par défaut du vendeur
    )


    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'email', 'phone', 'password', 'custom_permissions']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Si c'est un nouveau vendeur (pas d'instance existante ou pas de permissions)
        if not self.instance.pk or not self.instance.custom_permissions:
            # Pré-cocher les permissions par défaut du vendeur
            self.initial['custom_permissions'] = ROLE_PERMISSIONS['SELLER']['permissions']
        else:
            # Pour un vendeur existant, utiliser ses permissions actuelles
            perms = self.instance.custom_permissions
            if isinstance(perms, list):
                self.initial['custom_permissions'] = perms
            elif isinstance(perms, dict):
                self.initial['custom_permissions'] = list(perms.keys())

    def clean(self):
        cleaned_data = super().clean()
        password = cleaned_data.get('password')
        confirm_password = cleaned_data.get('confirm_password')

        if password and confirm_password:
            if password != confirm_password:
                raise forms.ValidationError("Les mots de passe ne correspondent pas.")
        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)
        # Toujours stocker custom_permissions comme une liste
        perms = self.cleaned_data.get('custom_permissions', [])
        if isinstance(perms, dict):
            perms = list(perms.keys())
        instance.custom_permissions = perms
        
        # Définir le mot de passe
        if self.cleaned_data.get('password'):
            instance.set_password(self.cleaned_data['password'])
        
        if commit:
            instance.save()
        return instance

class HRForm(forms.ModelForm):
    password = forms.CharField(
        label="Mot de passe",
        widget=forms.PasswordInput(),
        required=True,
        help_text="Mot de passe pour le compte RH"
    )
    confirm_password = forms.CharField(
        label="Confirmer le mot de passe",
        widget=forms.PasswordInput(),
        required=True
    )

    ALL_PERMISSIONS = set(ROLE_PERMISSIONS['ADMIN']['permissions'] + ROLE_PERMISSIONS['HR']['permissions'])
    PERMISSION_CHOICES = [
        (perm, PERMISSIONS_FR.get(perm, perm))
        for perm in sorted(ALL_PERMISSIONS)
    ]
    custom_permissions = forms.MultipleChoiceField(
        choices=PERMISSION_CHOICES,
        widget=forms.CheckboxSelectMultiple,
        required=False,
        label="Permissions d'accès",
        initial=ROLE_PERMISSIONS['HR']['permissions']
    )

    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'email', 'phone', 'password', 'custom_permissions']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if not self.instance.pk or not self.instance.custom_permissions:
            self.initial['custom_permissions'] = ROLE_PERMISSIONS['HR']['permissions']
        else:
            perms = self.instance.custom_permissions
            if isinstance(perms, list):
                self.initial['custom_permissions'] = perms
            elif isinstance(perms, dict):
                self.initial['custom_permissions'] = list(perms.keys())

    def clean(self):
        cleaned_data = super().clean()
        password = cleaned_data.get('password')
        confirm_password = cleaned_data.get('confirm_password')
        if password and confirm_password:
            if password != confirm_password:
                raise forms.ValidationError("Les mots de passe ne correspondent pas.")
        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)
        perms = self.cleaned_data.get('custom_permissions', [])
        if isinstance(perms, dict):
            perms = list(perms.keys())
        instance.custom_permissions = perms
        if self.cleaned_data.get('password'):
            instance.set_password(self.cleaned_data['password'])
        instance.role = 'HR'
        if commit:
            instance.save()
        return instance
