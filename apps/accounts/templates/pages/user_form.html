<h2><PERSON><PERSON><PERSON> un {{ role }}</h2>
<form method="post">
  {% csrf_token %}
  {{ form.non_field_errors }}
  <label>Email :</label>
  {{ form.email }}<br>
  <label>Prénom :</label>
  {{ form.first_name }}<br>
  <label>Nom :</label>
  {{ form.last_name }}<br>
  <label>Mot de passe :</label>
  {{ form.password }}<br>
  <label>Confirmer le mot de passe :</label>
  {{ form.confirm_password }}<br>
  <label>Téléphone :</label>
  {{ form.phone }}<br>
  <label>Permissions personnalisées :</label>
  <div style="margin-bottom: 1em;">
    {{ form.custom_permissions }}
  </div>
  {% if role == 'vendeur' and caisses_disponibles %}
    <label>Caisse à attribuer :</label>
    <select name="caisse_id" required>
      <option value="">-- Choisir une caisse --</option>
      {% for caisse in caisses_disponibles %}
        <option value="{{ caisse.id }}">{{ caisse.register_name }} ({{ caisse.register_number }})</option>
      {% endfor %}
    </select><br>
  {% endif %}

  <!-- Affichage détaillé des erreurs de formulaire -->
  {% if form.errors %}
    <div class="alert alert-danger">
      {% if form.non_field_errors %}
        <div>{{ form.non_field_errors }}</div>
      {% endif %}
      <ul>
      {% for field in form %}
        {% for error in field.errors %}
          <li><strong>{{ field.label }}:</strong> {{ error }}</li>
        {% endfor %}
      {% endfor %}
      </ul>
    </div>
  {% endif %}

  <button type="submit">Créer</button>
</form>
<a href="{% url 'accounts:user_list' %}">Retour à la liste</a> 
