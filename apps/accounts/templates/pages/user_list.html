{% extends "base/base.html" %}
{% block title %}Gestion des Vendeurs{% endblock title %}
{% load static %}
{% load user_filters %}

{% block content %}
{% block extra_scripts %}
<script src="{% static 'assets/js/user-form.js' %}"></script>
{% endblock extra_scripts %}
<div class="main-wrapper">
    {% include 'includes/header.html' %}
    {% include 'includes/sidebar.html' %}
    <div class="page-wrapper">
        <div class="content">
            <div class="page-header">
                <div class="add-item d-flex">
                    <div class="page-title">
                        <h4 class="fw-bold">Gestion des Vendeurs</h4>
                        <h6>Vous avez au total {{ users.count }} <b>Vendeur(s)</b></h6>
                    </div>
                </div>
                <div class="page-btn">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="openUserModal('vendeur')"><i class="ti ti-circle-plus me-1"></i> Ajouter un vendeur</button>
                    <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="openUserModal('grh')"><i class="ti ti-user-plus me-1"></i> Ajouter un GRH</button>
                    {% comment %} <button type="button" class="btn btn-sm btn-outline-secondary" onclick="openUserModal('comptable')"><i class="ti ti-circle-plus me-1"></i> Ajouter un comptable</button> {% endcomment %}
                    {% comment %} <button type="button" class="btn btn-sm btn-outline-secondary" onclick="openUserModal('caissier')"><i class="ti ti-circle-plus me-1"></i> Ajouter un caissier</button> {% endcomment %}
                    {% comment %} <button type="button" class="btn btn-sm btn-outline-secondary" onclick="openUserModal('magasinier')"><i class="ti ti-circle-plus me-1"></i> Ajouter un magasinier</button> {% endcomment %}
                    {% comment %} <button type="button" class="btn btn-sm btn-outline-secondary" onclick="openUserModal('manager')"><i class="ti ti-circle-plus me-1"></i> Ajouter un gérant</button> {% endcomment %}
                </div>
            </div>
            <div class="row">
                <div class="col-12 d-flex">
                    <div class="card w-100">
                        <div class="card-body">
                            <ul class="nav nav-tabs nav-tabs-bottom mb-3" id="userTabs" role="tablist">
                              {% for code, label in ROLES %}
                                <li class="nav-item" role="presentation">
                                  <a class="nav-link {% if selected_role == code %}active{% endif %}" href="?role={{ code }}">{{ label }}</a>
                                </li>
                              {% endfor %}
                            </ul>
                            <div class="tab-content" id="userTabsContent">
                              <div class="tab-pane fade show active" id="{{ selected_role|lower }}" role="tabpanel">
                                {% include 'pages/user_list_table.html' with users=users %}
                              </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Modal d'ajout d'utilisateur (contenu chargé dynamiquement) -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-md">
    <div class="modal-content" id="addUserModalContent">
      <!-- Le contenu du formulaire sera chargé ici dynamiquement -->
    </div>
  </div>
</div>

<script>
function openUserModal(role) {
    // Affiche un loader pendant le chargement
    const modalContent = document.getElementById('addUserModalContent');
    modalContent.innerHTML = `
      <div class="modal-header">
        <h5 class="modal-title" id="addUserModalLabel">Chargement...</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fermer"></button>
      </div>
      <div class="modal-body text-center">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Chargement...</span>
        </div>
        <p class="mt-2">Chargement du formulaire...</p>
      </div>`;
    var modal = new bootstrap.Modal(document.getElementById('addUserModal'));
    modal.show();
    // Charge dynamiquement le formulaire selon le rôle
    fetch(`/accounts/users/add/${role}/?modal=1`)
      .then(response => response.text())
      .then(html => {
        modalContent.innerHTML = html;
      })
      .catch(() => {
        modalContent.innerHTML = '<div class="modal-body text-danger">Erreur lors du chargement du formulaire.</div>';
      });
}
</script>

{% endblock content %} 
