from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.db import models
from django.utils.translation import gettext_lazy as _
from shortuuid.django_fields import ShortUUIDField

class Role(models.Model):
    name = models.CharField(_('Nom du rôle'), max_length=50, unique=True)
    description = models.TextField(_('Description'), blank=True)
    permissions = models.JSONField(_('Permissions'), default=dict)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Rôle')
        verbose_name_plural = _('Rôles')

    def __str__(self):
        return self.name

class UserManager(BaseUserManager):
    def create_user(self, email, password=None, **extra_fields):
        if not email:
            raise ValueError('L\'adresse email est obligatoire')
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_active', True)
        extra_fields.setdefault('role', 'ADMIN')

        return self.create_user(email, password, **extra_fields)

import uuid

class User(AbstractUser):
    # Permissions personnalisées pour les vendeurs (stockées en JSON)
    custom_permissions = models.JSONField(null=True, blank=True, default=dict, help_text="Permissions personnalisées pour ce vendeur")
    ROLES = [
        ('ADMIN', 'Administrateur'),
        ('MANAGER', 'Gérant'),
        ('CASHIER', 'Caissier'),
        ('SELLER', 'Vendeur'),
        ('STOCK_MANAGER', 'Gestionnaire de stock'),
        ('HR', 'Gestionnaire RH'),
    ]

    reference = ShortUUIDField(
        length=10,
        max_length=25,
        alphabet="0123456789",
        unique=True,
        editable=False
    )
    uuid = models.UUIDField(default=uuid.uuid4, editable=False, null=True, blank=True)
    
    username = None  # Désactiver le champ username
    email = models.EmailField(_('Adresse email'), unique=True)
    phone = models.CharField(_('Téléphone'), max_length=20, blank=True)
    address = models.TextField(_('Adresse'), blank=True)
    role = models.CharField(
        _('Rôle'),
        max_length=20,
        choices=ROLES,
        default='SELLER'
    )
    
    # Photo de profil
    avatar = models.ImageField(
        upload_to='avatars/',
        null=True,
        blank=True,
        verbose_name=_('Photo de profil')
    )
    
    # Informations de connexion
    last_login_ip = models.GenericIPAddressField(null=True, blank=True)
    last_login_device = models.CharField(max_length=255, blank=True)
    
    # Paramètres utilisateur
    theme_preference = models.CharField(
        max_length=10,
        choices=[('LIGHT', 'Clair'), ('DARK', 'Sombre')],
        default='LIGHT'
    )
    language_preference = models.CharField(
        max_length=10,
        choices=[('fr', 'Français'), ('en', 'English')],
        default='fr'
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Configuration
    objects = UserManager()
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['first_name', 'last_name']

    class Meta:
        verbose_name = _('Utilisateur')
        verbose_name_plural = _('Utilisateurs')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.get_full_name()} ({self.email})"

    def get_full_name(self):
        return f"{self.first_name} {self.last_name}".strip()

    @property
    def is_admin(self):
        return self.role == 'ADMIN'

    # @property
    # def is_manager(self):
    #     return self.role == 'MANAGER'

    # @property
    # def is_cashier(self):
    #     return self.role == 'CASHIER'


    @property
    def is_seller(self):
        return self.role == 'SELLER'

    @property
    def is_hr(self):
        return self.role == 'HR'

    # @property
    # def is_stock_manager(self):
    #     return self.role == 'STOCK_MANAGER'

    def has_permission(self, permission):
        """
        Vérifie si l'utilisateur a une permission spécifique
        """
        from .permissions import has_permission
        return has_permission(self, permission)

    def can_access_section(self, section):
        """
        Vérifie si l'utilisateur peut accéder à une section
        """
        from .permissions import can_access_section
        return can_access_section(self, section)

    def get_role_display_name(self):
        """
        Retourne le nom d'affichage du rôle
        """
        from .permissions import get_role_display_name
        return get_role_display_name(self.role)

class VendeurProfile(models.Model):
    user = models.OneToOneField('accounts.User', on_delete=models.CASCADE)
    # Ajoute ici les champs spécifiques au vendeur

# class ComptableProfile(models.Model):
#     user = models.OneToOneField('accounts.User', on_delete=models.CASCADE)
#     # Champs spécifiques au comptable

# class CaissierProfile(models.Model):
#     user = models.OneToOneField('accounts.User', on_delete=models.CASCADE)
#     # Champs spécifiques au caissier

# class MagasinierProfile(models.Model):
#     user = models.OneToOneField('accounts.User', on_delete=models.CASCADE)
#     # Champs spécifiques au magasinier
