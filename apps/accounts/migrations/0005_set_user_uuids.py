from django.db import migrations
import uuid

def set_user_uuids(apps, schema_editor):
	User = apps.get_model('accounts', 'User')
	for user in User.objects.all():
		if not user.uuid:
			user.uuid = uuid.uuid4()
			user.save(update_fields=['uuid'])

class Migration(migrations.Migration):
	dependencies = [
		('accounts', '0004_user_uuid'),
	]
	operations = [
		migrations.RunPython(set_user_uuids, reverse_code=migrations.RunPython.noop),
	]
