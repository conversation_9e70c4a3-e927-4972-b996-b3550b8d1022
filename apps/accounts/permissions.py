"""
Système de permissions basé sur les rôles utilisateur
"""

# Définition des permissions par rôle
ROLE_PERMISSIONS = {
    'ADMIN': {
        'name': 'Administrateur',
        'description': 'Accès complet à toutes les fonctionnalités',
        'permissions': [
            'dashboard',
            'clients',
            'grossistes', 
            'deposits',
            'suppliers',
            'shops',
            'products',
            'categories',
            'expired_products',
            'out_of_stock',
            'stock_reports',
            'sales',
            'invoice_templates',
            'returns',
            'purchases',
            'purchase_invoices',
            'expenses',
            'bank_accounts',
            'cash_register',
            'users',
            'reports',
        ]
    },
    'MANAGER': {
        'name': '<PERSON><PERSON><PERSON> (Directeur)',
        'description': 'Accès complet sauf gestion des utilisateurs (COMMENTÉ)',
        'permissions': [
            'dashboard',
            'clients',
            'grossistes', 
            'deposits',
            'suppliers',
            'shops',
            'products',
            'categories',
            'expired_products',
            'out_of_stock',
            'stock_reports',
            # 'sales',
            'invoice_templates',
            # 'returns',
            'purchases',
            'purchase_invoices',
            # 'expenses',
            'bank_accounts',
            'cash_register',
            'users',
            'reports',
        ]
    },
    'SELLER': {
        'name': 'Vendeur',
        'description': 'Accès limité aux ventes et clients',
        'permissions': [
            'dashboard',
            'clients',
            'deposits',
            # 'products',
            # 'categories',
            # 'expired_products',
            # 'out_of_stock',
            # 'stock_reports',
            'sales',
            'expenses',
            # 'returns',
        ]
    },
    'CASHIER': {
        'name': 'Caissier',
        'description': 'Accès aux ventes et caisse (COMMENTÉ)',
        'permissions': [
        ]
    },
    'STOCK_MANAGER': {
        'name': 'Gestionnaire de stock',
        'description': 'Accès à la gestion des stocks (COMMENTÉ)',
        'permissions': [
        ]
    },
    'HR': {
        'name': 'Gestionnaire RH',
        'description': 'Accès à la gestion du personnel, paie, SIRH, GRH',
        'permissions': [
            'dashboard',
            'sirh',
            'paie',
            'grh',
            'users',
            'reports',
        ]
    }
}

def has_permission(user, permission):
    """
    Vérifie si un utilisateur a une permission spécifique
    """
    if not user or not user.is_authenticated:
        return False
    
    # L'admin a toutes les permissions
    if user.role == 'ADMIN':
        return True
    
    # Vérifier les permissions du rôle
    role_permissions = ROLE_PERMISSIONS.get(user.role, {}).get('permissions', [])
    return permission in role_permissions

def get_user_permissions(user):
    """
    Retourne la liste des permissions d'un utilisateur
    """
    if not user or not user.is_authenticated:
        return []
    
    return ROLE_PERMISSIONS.get(user.role, {}).get('permissions', [])

def can_access_section(user, section):
    """
    Vérifie si un utilisateur peut accéder à une section de la sidebar
    """
    section_permissions = {
        'overview': ['dashboard'],
        'clients': ['clients', 'grossistes', 'deposits', 'suppliers', 'shops'],
        'stock': ['products', 'categories', 'expired_products', 'out_of_stock', 'stock_reports'],
        'sales': ['sales', 'invoice_templates', 'returns'],
        'purchases': ['purchases', 'purchase_invoices'],
        'finance': ['expenses', 'bank_accounts', 'cash_register'],
        'users': ['users'],
        'reports': ['reports'],
    }
    
    required_permissions = section_permissions.get(section, [])
    
    # Si aucune permission requise, refuser l'accès
    if not required_permissions:
        return False
    
    # Vérifier si l'utilisateur a au moins une des permissions requises
    return any(has_permission(user, perm) for perm in required_permissions)

def get_role_display_name(role):
    """
    Retourne le nom d'affichage d'un rôle
    """
    return ROLE_PERMISSIONS.get(role, {}).get('name', role) 
