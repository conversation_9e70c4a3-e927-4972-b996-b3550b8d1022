import requests
from typing import Optional, Dict, Any

API_BASE_URL = "http://localhost:3000"

class HoursAPIError(Exception):
    """Exception personnalisée pour les erreurs d'appel à l'API Heures Supplémentaires."""
    pass

def calculate_hours(employe_id: str, date_debut: str, date_fin: str) -> Dict[str, Any]:
    """
    Calcule les heures supplémentaires pour un employé sur une période.
    :param employe_id: UUID de l'employé
    :param date_debut: Date de début (YYYY-MM-DD)
    :param date_fin: Date de fin (YYYY-MM-DD)
    :return: Résultat du calcul (dict)
    """
    url = f"{API_BASE_URL}/api/heures-supplementaires/calculer"
    data = {
        "employe_id": employe_id,
        "date_debut": date_debut,
        "date_fin": date_fin
    }
    try:
        resp = requests.post(url, json=data)
        resp.raise_for_status()
        return resp.json()
    except Exception as e:
        raise HoursAPIError(f"Erreur lors du calcul des heures supplémentaires: {e}")

def approve_hours(employe_id: str, date_debut: str, date_fin: str, approuve_par: str) -> Dict[str, Any]:
    """
    Approuve les heures supplémentaires pour un employé sur une période.
    :param employe_id: UUID de l'employé
    :param date_debut: Date de début (YYYY-MM-DD)
    :param date_fin: Date de fin (YYYY-MM-DD)
    :param approuve_par: Nom ou ID de l'approbateur
    :return: Réponse de l'API
    """
    url = f"{API_BASE_URL}/api/heures-supplementaires/approuver"
    data = {
        "employe_id": employe_id,
        "date_debut": date_debut,
        "date_fin": date_fin,
        "approuve_par": approuve_par
    }
    try:
        resp = requests.post(url, json=data)
        resp.raise_for_status()
        return resp.json()
    except Exception as e:
        raise HoursAPIError(f"Erreur lors de l'approbation des heures supplémentaires: {e}")
