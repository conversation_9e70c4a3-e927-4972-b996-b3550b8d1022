// hours.js - gestion du calcul des heures supplémentaires

function getCsrfToken() {
    return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
}

$(function() {
    $('#btn-calc-hours').on('click', function() {
        const employeId = $('#employe-select').val();
        const dateDebut = $('#date-debut').val();
        const dateFin = $('#date-fin').val();
        const $result = $('#hours-result');
        $result.html('');

        if (!employeId || !dateDebut || !dateFin) {
            $result.html('<div class="alert alert-warning">Veuillez remplir tous les champs.</div>');
            return;
        }

        $result.html('<div class="text-info">Calcul en cours...</div>');

    fetch('/hours/api/hours/calculate/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify({
                employe_id: employeId,
                date_debut: dateDebut,
                date_fin: dateFin
            })
        })
        .then(r => r.json())
        .then(data => {
            if (data.success && data.data) {
                // Affichage simple, à adapter selon la structure de data.data
                $result.html('<pre>' + JSON.stringify(data.data, null, 2) + '</pre>');
                // Afficher la section d'approbation
                $('#approve-section').show();
                // Stocker les infos pour l'approbation
                $('#approve-section').data('employe-id', employeId);
                $('#approve-section').data('date-debut', dateDebut);
                $('#approve-section').data('date-fin', dateFin);
            } else {
                $result.html('<div class="alert alert-danger">' + (data.error || 'Erreur lors du calcul') + '</div>');
                $('#approve-section').hide();
            }
        })
        .catch(() => {
            $result.html('<div class="alert alert-danger">Erreur de connexion au serveur.</div>');
            $('#approve-section').hide();
        });
    });

    // Approbation des heures
    $('#btn-approve-hours').on('click', function() {
        const employeId = $('#approve-section').data('employe-id');
        const dateDebut = $('#approve-section').data('date-debut');
        const dateFin = $('#approve-section').data('date-fin');
    const approuvePar = window.USER_REFERENCE || $('#approuve-par').val();
        const $approveResult = $('#approve-result');
        $approveResult.html('');
        if (!employeId || !dateDebut || !dateFin || !approuvePar) {
            $approveResult.html('<div class="alert alert-warning">Veuillez remplir tous les champs pour l\'approbation.</div>');
            return;
        }
        $approveResult.html('<div class="text-info">Approbation en cours...</div>');
    fetch('/hours/api/hours/approve/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify({
                employe_id: employeId,
                date_debut: dateDebut,
                date_fin: dateFin,
                approuve_par: approuvePar
            })
        })
        .then(r => r.json())
        .then(data => {
            if (data.success) {
                $approveResult.html('<div class="alert alert-success">Heures approuvées avec succès.</div>');
            } else {
                $approveResult.html('<div class="alert alert-danger">' + (data.error || 'Erreur lors de l\'approbation') + '</div>');
            }
        })
        .catch(() => {
            $approveResult.html('<div class="alert alert-danger">Erreur de connexion au serveur.</div>');
        });
    });
});
