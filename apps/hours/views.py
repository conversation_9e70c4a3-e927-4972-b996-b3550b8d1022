from django.http import JsonResponse
from django.views import View
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import json
import logging

from .services import calculate_hours, approve_hours, HoursAPIError

from django.shortcuts import render

from django.contrib.auth.decorators import login_required

logger = logging.getLogger(__name__)

@method_decorator(csrf_exempt, name='dispatch')
class CalculateHoursApiView(LoginRequiredMixin, View):
	"""API pour calculer les heures supplémentaires d'un employé sur une période"""
	def post(self, request):
		try:
			data = json.loads(request.body)
			employe_id = data.get('employe_id')
			date_debut = data.get('date_debut')
			date_fin = data.get('date_fin')
			if not (employe_id and date_debut and date_fin):
				return JsonResponse({
					'success': False,
					'error': 'Champs requis manquants'
				}, status=400)
			result = calculate_hours(employe_id, date_debut, date_fin)
			return JsonResponse({
				'success': True,
				'data': result
			})
		except HoursAPIError as e:
			logger.error(f"Erreur API lors du calcul des heures: {e}")
			return JsonResponse({'success': False, 'error': str(e)}, status=500)
		except Exception as e:
			logger.error(f"Erreur lors du calcul des heures: {e}")
			return JsonResponse({'success': False, 'error': 'Erreur interne du serveur'}, status=500)

@method_decorator(csrf_exempt, name='dispatch')
class ApproveHoursApiView(LoginRequiredMixin, View):
	"""API pour approuver les heures supplémentaires d'un employé sur une période"""
	def post(self, request):
		try:
			data = json.loads(request.body)
			employe_id = data.get('employe_id')
			date_debut = data.get('date_debut')
			date_fin = data.get('date_fin')
			approuve_par = data.get('approuve_par')
			if not (employe_id and date_debut and date_fin and approuve_par):
				return JsonResponse({
					'success': False,
					'error': 'Champs requis manquants'
				}, status=400)
			result = approve_hours(employe_id, date_debut, date_fin, approuve_par)
			return JsonResponse({
				'success': True,
				'data': result
			})
		except HoursAPIError as e:
			logger.error(f"Erreur API lors de l'approbation des heures: {e}")
			return JsonResponse({'success': False, 'error': str(e)}, status=500)
		except Exception as e:
			logger.error(f"Erreur lors de l'approbation des heures: {e}")
			return JsonResponse({'success': False, 'error': 'Erreur interne du serveur'}, status=500)

@login_required
def hours_calculator_view(request):
	"""Vue pour afficher la page de calcul des heures supplémentaires"""
	user_reference = str(getattr(request.user, 'uuid', ''))
	return render(request, 'hours/hours_calculator.html', {'user_reference': user_reference})
