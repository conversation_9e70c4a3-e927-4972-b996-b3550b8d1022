{% extends 'base/base.html' %}
{% load static %}

{% block content %}
<div class="main-wrapper">
    {% include 'includes/header.html' %}
    {% include 'includes/sidebar.html' %}
    <div class="page-wrapper">
        <div class="container-fluid mt-4">
    <h2>Calcul des Heures Supplémentaires</h2>
    <form id="hours-form" class="row g-3">
        <div class="col-md-4">
            <label for="employe-select" class="form-label">Employé</label>
            <select id="employe-select" class="form-select" required>
                <option value="">Sélectionner...</option>
                <!-- Options dynamiques -->
            </select>
        </div>
        <div class="col-md-3">
            <label for="date-debut" class="form-label">Date début</label>
            <input type="date" id="date-debut" class="form-control" required>
        </div>
        <div class="col-md-3">
            <label for="date-fin" class="form-label">Date fin</label>
            <input type="date" id="date-fin" class="form-control" required>
        </div>
        <div class="col-md-2 d-flex align-items-end">
            <button type="button" id="btn-calc-hours" class="btn btn-primary w-100">Calculer</button>
        </div>
    </form>
    <div id="hours-result" class="mt-4"></div>
    <div id="approve-section" class="mt-4" style="display:none;">
        <div class="row g-2 align-items-end">
            <div class="col-md-4">
                <label for="approuve-par" class="form-label">Approuvé par</label>
                <input type="text" id="approuve-par" class="form-control" placeholder="Nom du responsable">
            </div>
            <div class="col-md-2">
                <button type="button" id="btn-approve-hours" class="btn btn-success w-100">Approuver les heures</button>
            </div>
        </div>
        <div id="approve-result" class="mt-2"></div>
    </div>
</div>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
// Expose l'UUID de l'utilisateur connecté pour le JS
window.USER_REFERENCE = "{{ user_reference|default:'' }}";
alert(window.USER_REFERENCE)
</script>
<script src="{% static 'hours/js/hours.js' %}"></script>
<script>
// Exemple de chargement dynamique des employés (à adapter selon ton API)
$(function() {
    fetch('/sirh/api/employes/')
        .then(r => r.json())
        .then(data => {
            if (data.success && data.data) {
                data.data.forEach(emp => {
                    $('#employe-select').append(`<option value="${emp.id}">${emp.nom} ${emp.prenom}</option>`);
                });
            }
        });
});
</script>
{% endblock %}
