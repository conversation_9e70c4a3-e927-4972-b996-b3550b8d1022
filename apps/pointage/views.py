

from django.shortcuts import render
from django.views import View
from django.views.generic import TemplateView
from django.http import JsonResponse
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

from apps.pointage.services import PointageService

# Page principale de pointage
class PointageListView(LoginRequiredMixin, TemplateView):
	template_name = 'pointage/pointage_list.html'

# API proxy pour récupérer la liste des employés via le service métier PointageService

@method_decorator(csrf_exempt, name='dispatch')
class EmployesSIRHApiView(LoginRequiredMixin, View):
	def get(self, request):
		service = PointageService()
		try:
			data = service.get_employes()
			return JsonResponse(data, safe=False)
		except Exception as e:
			import traceback
			tb = traceback.format_exc()
			return JsonResponse({'error': str(e), 'traceback': tb}, status=500)


# API pour enregistrer et lister les pointages d'un employé
@method_decorator(csrf_exempt, name='dispatch')
class PointageApiView(LoginRequiredMixin, View):
	def post(self, request):
		import json, traceback
		service = PointageService()
		try:
			payload = json.loads(request.body.decode('utf-8'))
			employe_id = payload.get('employe_id')
			type = payload.get('type')
			date = payload.get('date')  # optionnel, format YYYY-MM-DD
			heure = payload.get('heure')  # optionnel, format HH:MM:SS
			commentaire = payload.get('commentaire')
			pointage = service.enregistrer_pointage(employe_id, type, date, heure, commentaire)
			return JsonResponse(pointage, status=201, safe=False)
		except Exception as e:
			tb = traceback.format_exc()
			return JsonResponse({'error': str(e), 'traceback': tb}, status=500)

	def get(self, request):
		import traceback
		service = PointageService()
		employe_id = request.GET.get('employe_id')
		date = request.GET.get('date')  # optionnel
		try:
			if not employe_id:
				return JsonResponse({'error': 'employe_id requis'}, status=400)
			pointages = service.lister_pointages_employe(employe_id, date)
			return JsonResponse(pointages, safe=False)
		except Exception as e:
			tb = traceback.format_exc()
			return JsonResponse({'error': str(e), 'traceback': tb}, status=500)
