"""
Services pour la gestion du pointage (intégration SIRH, logique métier, etc.)
"""

from apps.sirh.services.sirh_api import SirhApiService

class PointageService:
    """
    Service métier pour le pointage : consomme l'API SIRH pour tous les pointages (pas de modèle local).
    """
    def __init__(self):
        self.sirh = SirhApiService()

    def get_employes(self):
        return self.sirh.get_employes()

    def enregistrer_pointage(self, employe_id, type, date=None, heure=None, commentaire=None, pause_debut=None, pause_fin=None):
        """Enregistre un pointage via l'API SIRH (mapping strict spec API SIRH)."""
        data = {
            "employe_id": employe_id,
            "date": date,
            "statut": "PRESENT",
            "commentaire": commentaire,
        }
        if type == "ENTREE":
            data["heure_arrivee"] = heure
        elif type == "SORTIE":
            data["heure_depart"] = heure
        if pause_debut:
            data["pause_debut"] = pause_debut
        if pause_fin:
            data["pause_fin"] = pause_fin
        # Nettoyage des champs vides
        data = {k: v for k, v in data.items() if v is not None and v != ''}
        return self.sirh.create_pointage(data)

    def lister_pointages_employe(self, employe_id, date=None):
        """Liste les pointages d'un employé via l'API SIRH."""
        params = {"employe_id": employe_id}
        if date:
            params["date"] = date
        return self.sirh.get_pointages(params)
