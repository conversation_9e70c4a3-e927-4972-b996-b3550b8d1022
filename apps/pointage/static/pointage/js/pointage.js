// Chargement des employés via l'API interne pointage (proxy SIRH)

document.addEventListener('DOMContentLoaded', function() {
    // Chargement des employés
    fetch('/pointage/api/employes/')
        .then(response => response.json())
        .then(result => {
            const select = document.getElementById('employe-select');
            select.innerHTML = '<option value="">-- Choisir un employé --</option>';
            let employes = Array.isArray(result) ? result : (result.data || []);
            employes.forEach(emp => {
                select.innerHTML += `<option value="${emp.id}">${emp.nom} ${emp.prenom}</option>`;
            });
        });

    document.getElementById('employe-select').addEventListener('change', function() {
        const employeId = this.value;
        const form = document.getElementById('form-pointage');
        const tableDiv = document.getElementById('table-pointages');
        if (!employeId) {
            form.style.display = 'none';
            tableDiv.innerHTML = '';
            return;
        }
        form.style.display = '';
        // Préremplir date/heure actuelles
        document.getElementById('date-pointage').value = new Date().toISOString().slice(0,10);
        document.getElementById('heure-pointage').value = new Date().toTimeString().slice(0,5);
        chargerPointages(employeId);
    });

    // Soumission du formulaire de pointage
    document.getElementById('form-pointage').addEventListener('submit', function(e) {
        e.preventDefault();
        const employeId = document.getElementById('employe-select').value;
        const type = document.getElementById('type-pointage').value;
        let date = document.getElementById('date-pointage').value;
        if (date) {
            if (date.includes('T')) date = date.split('T')[0];
            if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
                const d = new Date(date);
                if (!isNaN(d)) {
                    const mm = (d.getMonth()+1).toString().padStart(2,'0');
                    const dd = d.getDate().toString().padStart(2,'0');
                    date = `${d.getFullYear()}-${mm}-${dd}`;
                }
            }
        }
        const heure = document.getElementById('heure-pointage').value;
        const commentaire = document.getElementById('commentaire-pointage').value;
        const pauseDebut = document.getElementById('pause_debut') ? document.getElementById('pause_debut').value : undefined;
        const pauseFin = document.getElementById('pause_fin') ? document.getElementById('pause_fin').value : undefined;
        const messageDiv = document.getElementById('pointage-message');
        // Construction dynamique du body conforme à l'API SIRH
        const rawBody = {
            employe_id: employeId,
            date_pointage: date,
            statut: "PRESENT",
            commentaire: commentaire && commentaire.trim() !== "" ? commentaire : undefined,
            heure_arrivee: type === "ENTREE" ? heure : undefined,
            heure_depart: type === "SORTIE" ? heure : undefined,
            pause_debut: pauseDebut && pauseDebut.trim() !== "" ? pauseDebut : undefined,
            pause_fin: pauseFin && pauseFin.trim() !== "" ? pauseFin : undefined
        };
        // Nettoyage: supprimer les champs vides/undefined et forcer string
        const body = {};
        Object.keys(rawBody).forEach(key => {
            if (rawBody[key] !== undefined && rawBody[key] !== null && rawBody[key] !== "") {
                body[key] = String(rawBody[key]);
            }
        });
        console.log('POST /pointage/api/pointages body:', JSON.stringify(body));
        fetch('/pointage/api/pointages/', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(body)
        })
        .then(async r => {
            let data;
            try { data = await r.json(); } catch { data = {}; }
            if (!r.ok) {
                let msg = data.error || r.statusText;
                if (data.traceback) msg += `<br><pre>${data.traceback}</pre>`;
                throw new Error(msg);
            }
            return data;
        })
        .then(data => {
            messageDiv.innerHTML = `<div class='alert alert-success'>Pointage enregistré !</div>`;
            chargerPointages(employeId);
        })
        .catch(e => {
            messageDiv.innerHTML = `<div class='alert alert-danger'>${e.message}</div>`;
        });
    });
});

function chargerPointages(employeId) {
    const tableDiv = document.getElementById('table-pointages');
    tableDiv.innerHTML = '<div>Chargement...</div>';
    fetch(`/pointage/api/pointages/?employe_id=${encodeURIComponent(employeId)}`)
        .then(r => r.json())
        .then(data => {
            if (data.error) {
                tableDiv.innerHTML = `<div class='alert alert-danger'>${data.error}</div>`;
                return;
            }
            if (!Array.isArray(data) || data.length === 0) {
                tableDiv.innerHTML = '<div class="alert alert-info">Aucun pointage trouvé.</div>';
                return;
            }
            let html = `<table class="table table-bordered table-sm mt-3"><thead><tr><th>Date</th><th>Heure</th><th>Type</th><th>Commentaire</th></tr></thead><tbody>`;
            data.forEach(p => {
                html += `<tr><td>${p.date}</td><td>${p.heure}</td><td>${p.type}</td><td>${p.commentaire||''}</td></tr>`;
            });
            html += '</tbody></table>';
            tableDiv.innerHTML = html;
        })
        .catch(() => {
            tableDiv.innerHTML = `<div class='alert alert-danger'>Erreur lors du chargement des pointages.</div>`;
        });
}
