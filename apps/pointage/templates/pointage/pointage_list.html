{% extends 'base/base.html' %}
{% load static %}
{% block content %}
<div class="main-wrapper">
    {% include 'includes/header.html' %}
    {% include 'includes/sidebar.html' %}
    <div class="page-wrapper">
        <div class="container mt-4">
            <h1>Pointage</h1>
            <div id="pointage-app">
                <div class="mb-3">
                    <label for="employe-select" class="form-label">Sélectionner un employé :</label>
                    <select id="employe-select" class="form-select"></select>
                </div>
                <div id="pointage-content">
                    <!-- Formulaire de pointage -->
                    <form id="form-pointage" class="row g-3 mb-4" style="display:none;">
                        <div class="col-md-3">
                            <label for="type-pointage" class="form-label">Type</label>
                            <select id="type-pointage" class="form-select" required>
                                <option value="ENTREE">Entrée</option>
                                <option value="SORTIE">Sortie</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="date-pointage" class="form-label">Date</label>
                            <input type="date" id="date-pointage" class="form-control" required>
                        </div>
                        <div class="col-md-3">
                            <label for="heure-pointage" class="form-label">Heure</label>
                            <input type="time" id="heure-pointage" class="form-control" required>
                        </div>
                        <div class="col-md-3">
                            <label for="commentaire-pointage" class="form-label">Commentaire</label>
                            <input type="text" id="commentaire-pointage" class="form-control">
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">Enregistrer le pointage</button>
                        </div>
                        <div id="pointage-message" class="mt-2"></div>
                    </form>
                    <!-- Tableau des pointages -->
                    <div id="table-pointages"></div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="{% static 'pointage/js/pointage.js' %}"></script>
{% endblock %}
