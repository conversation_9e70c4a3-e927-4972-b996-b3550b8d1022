from django import forms

class LoanCreateForm(forms.Form):
    employeId = forms.CharField(label="Employé", required=True, widget=forms.TextInput(attrs={
        'autocomplete': 'off',
        'class': 'form-control',
        'id': 'employe-search',
        'placeholder': 'Rechercher un employé...'
    }))
    montantInitial = forms.DecimalField(label="Montant initial", required=True, min_value=0)
    dureeRemboursement = forms.IntegerField(label="Durée de remboursement (mois)", required=True, min_value=1)
    tauxInteret = forms.DecimalField(label="Taux d'intérêt", required=True, min_value=0, max_value=1, help_text="Ex: 0.05 pour 5%")
    motif = forms.CharField(label="Motif", required=False)
    garantie = forms.CharField(label="Garantie", required=False)

class LoanSimulateForm(forms.Form):
    montantInitial = forms.DecimalField(label="Montant initial", required=True, min_value=0)
    dureeRemboursement = forms.IntegerField(label="Durée de remboursement (mois)", required=True, min_value=1)
    tauxInteret = forms.DecimalField(label="Taux d'intérêt", required=True, min_value=0, max_value=1, help_text="Ex: 0.05 pour 5%")

class LoanRepayForm(forms.Form):
    montant = forms.DecimalField(label="Montant du remboursement", required=True, min_value=0)

class LoanStatusForm(forms.Form):
    statut = forms.ChoiceField(label="Nouveau statut", choices=[
        ("EN_COURS", "En cours"),
        ("SUSPENDU", "Suspendu"),
        ("TERMINE", "Terminé"),
    ])
