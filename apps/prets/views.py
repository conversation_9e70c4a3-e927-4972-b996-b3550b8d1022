import logging
logger = logging.getLogger(__name__)

from django.views import View
from django.shortcuts import render, redirect
from django.urls import reverse
from django.contrib import messages

from . import services
from .forms import LoanCreateForm, LoanSimulateForm, LoanRepayForm, LoanStatusForm

class LoanListView(View):
	def get(self, request):
		from .services_employes import get_all_employes
		employe_id = request.GET.get('employeId')
		try:
			employes = get_all_employes()
		except Exception as e:
			employes = {'data': []}
		loans = services.get_loans(employe_id)
		return render(request, 'prets/loan_list.html', {'loans': loans, 'employes': employes})

class LoanDetailView(View):
	def get(self, request, pk):
		loan = services.get_loan_detail(pk)
		return render(request, 'prets/loan_detail.html', {'loan': loan})

class LoanCreateView(View):
	def get(self, request):
		from .services_employes import get_all_employes
		form = LoanCreateForm()
		try:
			employes = get_all_employes()
		except Exception as e:
			employes = []
			logger.error("Erreur lors de la récupération des employés: %s", e)
		logger.debug("LoanCreateView GET - form initialisé: %s", form)
		return render(request, 'prets/loan_form.html', {'form': form, 'employes': employes})

	def post(self, request):
		form = LoanCreateForm(request.POST)
		logger.debug("LoanCreateView POST - data reçue: %s", request.POST)
		if form.is_valid():
			try:
				logger.debug("LoanCreateView POST - form.cleaned_data: %s", form.cleaned_data)
				result = services.create_loan(form.cleaned_data)
				logger.debug("LoanCreateView POST - résultat API: %s", result)
				messages.success(request, "Prêt créé avec succès.")
				return redirect(reverse('prets:loan_list'))
			except Exception as e:
				logger.exception("Erreur lors de la création du prêt (sérialisation ou API)")
				messages.error(request, str(e))
		else:
			logger.warning("LoanCreateView POST - Formulaire invalide: %s", form.errors)
		return render(request, 'prets/loan_form.html', {'form': form})

class LoanSimulateView(View):
	def get(self, request):
		form = LoanSimulateForm()
		logger.debug("LoanSimulateView GET - form initialisé: %s", form)
		return render(request, 'prets/loan_simulate.html', {'form': form})

	def post(self, request):
		form = LoanSimulateForm(request.POST)
		logger.debug("LoanSimulateView POST - data reçue: %s", request.POST)
		result = None
		if form.is_valid():
			cd = form.cleaned_data
			logger.debug("LoanSimulateView POST - form.cleaned_data: %s", cd)
			try:
				result = services.simulate_loan(cd['montantInitial'], cd['tauxInteret'], cd['dureeRemboursement'])
				logger.debug("LoanSimulateView POST - résultat API: %s", result)
			except Exception as e:
				logger.exception("Erreur lors de la simulation du prêt (sérialisation ou API)")
				messages.error(request, str(e))
		else:
			logger.warning("LoanSimulateView POST - Formulaire invalide: %s", form.errors)
		return render(request, 'prets/loan_simulate.html', {'form': form, 'result': result})

class LoanRepayView(View):
	def post(self, request, pk):
		form = LoanRepayForm(request.POST)
		logger.debug("LoanRepayView POST - data reçue: %s", request.POST)
		if form.is_valid():
			montant = form.cleaned_data['montant']
			logger.debug("LoanRepayView POST - montant: %s", montant)
			try:
				result = services.repay_loan(pk, montant)
				logger.debug("LoanRepayView POST - résultat API: %s", result)
				messages.success(request, "Remboursement effectué.")
			except Exception as e:
				logger.exception("Erreur lors du remboursement du prêt (sérialisation ou API)")
				messages.error(request, str(e))
		else:
			logger.warning("LoanRepayView POST - Formulaire invalide: %s", form.errors)
			messages.error(request, "Formulaire de remboursement invalide.")
		return redirect(reverse('prets:loan_detail', args=[pk]))

class LoanStatusUpdateView(View):
	def post(self, request, pk):
		form = LoanStatusForm(request.POST)
		if form.is_valid():
			statut = form.cleaned_data['statut']
			try:
				services.update_loan_status(pk, statut)
				messages.success(request, "Statut modifié.")
			except Exception as e:
				messages.error(request, str(e))
		else:
			messages.error(request, "Formulaire de statut invalide.")
		return redirect(reverse('prets:loan_detail', args=[pk]))

class LoanScheduleView(View):
	def get(self, request, pk):
		schedule = services.get_loan_schedule(pk)
		return render(request, 'prets/loan_schedule.html', {'schedule': schedule})
