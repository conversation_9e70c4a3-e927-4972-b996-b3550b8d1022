from decimal import Decimal
import requests
from django.conf import settings

API_BASE_URL = getattr(settings, 'PAIE_API_BASE_URL', 'http://localhost:3000')

def get_loans(employe_id):
    """Liste des prêts d'un employé"""
    url = f"{API_BASE_URL}/api/prets"
    params = {}
    if employe_id:
        params["employeId"] = employe_id
    try:
        resp = requests.get(url, params=params)
        resp.raise_for_status()
        return resp.json()
    except requests.RequestException as e:
        raise Exception(f"Erreur lors de la récupération des prêts: {e}")

def get_loan_detail(loan_id):
    """Détail d'un prêt"""
    url = f"{API_BASE_URL}/api/prets/{loan_id}"
    try:
        resp = requests.get(url)
        resp.raise_for_status()
        return resp.json()
    except requests.RequestException as e:
        raise Exception(f"Erreur lors de la récupération du prêt: {e}")

def get_loan_schedule(loan_id):
    """Échéancier d'un prêt"""
    url = f"{API_BASE_URL}/api/prets/{loan_id}/echeances"
    try:
        resp = requests.get(url)
        resp.raise_for_status()
        return resp.json()
    except requests.RequestException as e:
        raise Exception(f"Erreur lors de la récupération de l'échéancier: {e}")

def simulate_loan(montant_initial, taux_interet, duree_remboursement):
    """Simulation de prêt"""
    url = f"{API_BASE_URL}/api/prets/simuler"
    data = {
        "montantInitial": float(montant_initial) if isinstance(montant_initial, Decimal) else montant_initial,
        "tauxInteret": float(taux_interet) if isinstance(taux_interet, Decimal) else taux_interet,
        "dureeRemboursement": duree_remboursement
    }
    try:
        resp = requests.post(url, json=data)
        resp.raise_for_status()
        return resp.json()
    except requests.RequestException as e:
        raise Exception(f"Erreur lors de la simulation du prêt: {e}")

def create_loan(data):
    """Création d'un prêt"""
    # Conversion sécurisée de tous les Decimal en float
    for k, v in data.items():
        if isinstance(v, Decimal):
            data[k] = float(v)
    url = f"{API_BASE_URL}/api/prets"
    try:
        resp = requests.post(url, json=data)
        resp.raise_for_status()
        return resp.json()
    except requests.RequestException as e:
        # Log la réponse brute de l'API si dispo
        print("Réponse API:", getattr(e.response, 'text', None))
        raise Exception(f"Erreur lors de la création du prêt: {e}")

def repay_loan(loan_id, montant):
    """Remboursement d'un prêt"""
    url = f"{API_BASE_URL}/api/prets/{loan_id}/remboursement"
    data = {"montant": float(montant) if isinstance(montant, Decimal) else montant}
    try:
        resp = requests.post(url, json=data)
        resp.raise_for_status()
        return resp.json()
    except requests.RequestException as e:
        raise Exception(f"Erreur lors du remboursement du prêt: {e}")

def update_loan_status(loan_id, statut):
    """Modification du statut d'un prêt"""
    url = f"{API_BASE_URL}/api/prets/{loan_id}/statut"
    data = {"statut": statut}
    try:
        resp = requests.put(url, json=data)
        resp.raise_for_status()
        return resp.json()
    except requests.RequestException as e:
        raise Exception(f"Erreur lors de la modification du statut du prêt: {e}")
