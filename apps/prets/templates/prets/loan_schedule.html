{% extends 'base/base.html' %}
{% load static %}
{% block content %}
<div class="main-wrapper">
    {% include 'includes/header.html' %}
    {% include 'includes/sidebar.html' %}
    <div class="page-wrapper">
        <div class="container mt-4">
          <h1>Échéancier du prêt</h1>
          <a href="{% url 'prets:loan_detail' schedule.data.pretId %}" class="btn btn-secondary mb-3">Retour au prêt</a>
          <table class="table table-bordered">
            <thead>
              <tr>
                <th>#</th>
                <th>Date</th>
                <th>Montant</th>
                <th>Statut</th>
              </tr>
            </thead>
            <tbody>
              {% for echeance in schedule.data %}
              <tr>
                <td>{{ forloop.counter }}</td>
                <td>{{ echeance.date }}</td>
                <td>{{ echeance.montant }}</td>
                <td>{{ echeance.statut }}</td>
              </tr>
              {% empty %}
              <tr><td colspan="4">Aucune échéance trouvée.</td></tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
    </div>
</div>
{% endblock %}

{% block script_block %}
<script src="{% static 'prets/js/debug.js' %}"></script>
{% endblock %}
