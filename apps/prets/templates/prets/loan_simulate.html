{% extends 'base/base.html' %}
{% load static %}
{% block content %}
<div class="main-wrapper">
    {% include 'includes/header.html' %}
    {% include 'includes/sidebar.html' %}
    <div class="page-wrapper">
        <div class="container mt-4">
          <h1>Simulation de prêt</h1>
          <a href="{% url 'prets:loan_list' %}" class="btn btn-secondary mb-3">Retour à la liste</a>
          <form method="post" novalidate>
            {% csrf_token %}
            {% if form.errors %}
              <div class="alert alert-danger">Veuillez corriger les erreurs du formulaire.</div>
            {% endif %}
            {% for field in form %}
              <div class="form-group mb-2">
                {{ field.label_tag }}
                {{ field }}
                {% if field.help_text %}<small class="form-text text-muted">{{ field.help_text }}</small>{% endif %}
                {% for error in field.errors %}
                  <div class="text-danger small">{{ error }}</div>
                {% endfor %}
              </div>
            {% endfor %}
            <button type="submit" class="btn btn-info mt-2">Simuler</button>
          </form>
          {% if result %}
          <hr>
          <h3>Résultat de la simulation</h3>
          <pre>{{ result|safe }}</pre>
          {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block script_block %}
<script src="{% static 'prets/js/debug.js' %}"></script>
{% endblock %}
