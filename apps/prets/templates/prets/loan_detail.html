{% extends 'base/base.html' %}
{% load static %}
{% block content %}
<div class="main-wrapper">
    {% include 'includes/header.html' %}
    {% include 'includes/sidebar.html' %}
    <div class="page-wrapper">
        <div class="container mt-4">
          <h1>Détail du prêt</h1>
          <a href="{% url 'prets:loan_list' %}" class="btn btn-secondary mb-3">Retour à la liste</a>
          <table class="table table-striped">
            <tr><th>ID</th><td>{{ loan.data.id }}</td></tr>
            <tr><th>Employé</th><td>{{ loan.data.employeId }}</td></tr>
            <tr><th>Montant initial</th><td>{{ loan.data.montantInitial }}</td></tr>
            <tr><th>Durée</th><td>{{ loan.data.dureeRemboursement }}</td></tr>
            <tr><th>Taux d'intérêt</th><td>{{ loan.data.tauxInteret }}</td></tr>
            <tr><th>Statut</th><td>{{ loan.data.statut }}</td></tr>
            <tr><th>Motif</th><td>{{ loan.data.motif }}</td></tr>
            <tr><th>Garantie</th><td>{{ loan.data.garantie }}</td></tr>
          </table>
          <a href="{% url 'prets:loan_schedule' loan.data.id %}" class="btn btn-outline-primary">Voir échéancier</a>
          <hr>
          <h3>Rembourser</h3>
          <form method="post" action="{% url 'prets:loan_repay' loan.data.id %}" novalidate>
            {% csrf_token %}
            {% with repay_form=repay_form|default:None %}
            {% if repay_form %}
              {% if repay_form.errors %}
                <div class="alert alert-danger">Erreur dans le formulaire de remboursement.</div>
              {% endif %}
              {% for field in repay_form %}
                <div class="form-group mb-2">
                  {{ field.label_tag }}
                  {{ field }}
                  {% for error in field.errors %}
                    <div class="text-danger small">{{ error }}</div>
                  {% endfor %}
                </div>
              {% endfor %}
            {% else %}
              <div class="form-group mb-2">
                <label for="id_montant">Montant</label>
                <input type="number" step="0.01" name="montant" class="form-control" required>
              </div>
            {% endif %}
            {% endwith %}
            <button type="submit" class="btn btn-success mt-2">Rembourser</button>
          </form>
          <hr>
          <h3>Changer le statut</h3>
          <form method="post" action="{% url 'prets:loan_status_update' loan.data.id %}" novalidate>
            {% csrf_token %}
            {% with status_form=status_form|default:None %}
            {% if status_form %}
              {% if status_form.errors %}
                <div class="alert alert-danger">Erreur dans le formulaire de statut.</div>
              {% endif %}
              {% for field in status_form %}
                <div class="form-group mb-2">
                  {{ field.label_tag }}
                  {{ field }}
                  {% for error in field.errors %}
                    <div class="text-danger small">{{ error }}</div>
                  {% endfor %}
                </div>
              {% endfor %}
            {% else %}
              <div class="form-group mb-2">
                <label for="id_statut">Statut</label>
                <select name="statut" class="form-control">
                  <option value="EN_COURS">En cours</option>
                  <option value="SUSPENDU">Suspendu</option>
                  <option value="TERMINE">Terminé</option>
                </select>
              </div>
            {% endif %}
            {% endwith %}
            <button type="submit" class="btn btn-warning mt-2">Mettre à jour</button>
          </form>
        </div>
    </div>
</div>
{% endblock %}

{% block script_block %}
<script src="{% static 'prets/js/debug.js' %}"></script>
{% endblock %}
