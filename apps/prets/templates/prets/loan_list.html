{% extends 'base/base.html' %}
{% load static %}
{% block content %}
<div class="main-wrapper">
    {% include 'includes/header.html' %}
    {% include 'includes/sidebar.html' %}
    <div class="page-wrapper">
        <div class="container mt-4">
          <h1>Liste des prêts</h1>
          <form method="get" class="mb-3 d-flex align-items-end" style="gap:1rem;">
            <div>
              <label for="employeId">Filtrer par employé :</label>
              <select name="employeId" id="employeId" class="form-control">
                <option value="">-- Tous --</option>
                {% for emp in employes.data %}
                  <option value="{{ emp.id }}" {% if request.GET.employeId == emp.id %}selected{% endif %}>{{ emp.nom }} {{ emp.prenom }}</option>
                {% endfor %}
              </select>
            </div>
            <button type="submit" class="btn btn-secondary">Filtrer</button>
            <a href="{% url 'prets:loan_create' %}" class="btn btn-primary">Nouveau prêt</a>
          </form>
          <table class="table table-bordered table-hover">
            <thead>
              <tr>
                <th>ID</th>
                <th>Employé</th>
                <th>Montant</th>
                <th>Durée</th>
                <th>Statut</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {% for loan in loans.data %}
              <tr>
                <td>{{ loan.id }}</td>
                <td>{{ loan.employeId }}</td>
                <td>{{ loan.montantInitial }}</td>
                <td>{{ loan.dureeRemboursement }}</td>
                <td>{{ loan.statut }}</td>
                <td>
                  <a href="{% url 'prets:loan_detail' loan.id %}" class="btn btn-sm btn-info">Détail</a>
                </td>
              </tr>
              {% empty %}
              <tr><td colspan="6">Aucun prêt trouvé.</td></tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
    </div>
</div>
{% endblock %}
