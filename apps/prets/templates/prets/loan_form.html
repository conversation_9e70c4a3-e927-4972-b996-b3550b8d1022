{% extends 'base/base.html' %}
{% load static %}

{% block content %}
<div class="main-wrapper">
    {% include 'includes/header.html' %}
    {% include 'includes/sidebar.html' %}
    <div class="page-wrapper">
        <div class="container mt-4">
            <h1><PERSON><PERSON>er un prêt</h1>
            <a href="{% url 'prets:loan_list' %}" class="btn btn-secondary mb-3">Retour à la liste</a>
            <form method="post" novalidate>
            {% csrf_token %}
            {% if form.errors %}
                <div class="alert alert-danger">Veuillez corriger les erreurs du formulaire.</div>
            {% endif %}
                        {# Champ de recherche employé avec suggestions #}
                        <div class="form-group mb-2 position-relative">
                            {{ form.employeId.label_tag }}
                            {{ form.employeId }}
                            <div id="employe-suggestions" class="list-group position-absolute w-100" style="z-index: 1000;"></div>
                            {% for error in form.employeId.errors %}
                              <div class="text-danger small">{{ error }}</div>
                            {% endfor %}
                        </div>
                                    {# DEBUG : Affichage brut de la liste des employés #}
            <pre style="max-height:200px;overflow:auto;background:#f8f9fa;border:1px solid #ddd;">{{ employes|safe }}</pre>

                        {# Select box employés #}
                        <div class="form-group mb-2">
                            <label for="employe-select">Sélectionner un employé</label>
                            <select id="employe-select" class="form-control">
                                <option value="">-- Choisir --</option>
                                {% for emp in employes.data %}
                                    <option value="{{ emp.id }}">{{ emp.nom }} {{ emp.prenom }}</option>
                                {% endfor %}
                            </select>
                        </div>
<script>
// Quand on sélectionne dans le select, on remplit le champ recherche
document.addEventListener('DOMContentLoaded', function() {
    const select = document.getElementById('employe-select');
    const input = document.getElementById('employe-search');
    if (select && input) {
        select.addEventListener('change', function() {
            if (this.value) {
                input.value = this.value;
            }
        });
    }
});
</script>
                        {# Les autres champs #}
                        {% for field in form %}
                            {% if field.name != 'employeId' %}
                            <div class="form-group mb-2">
                                {{ field.label_tag }}
                                {{ field }}
                                {% if field.help_text %}<small class="form-text text-muted">{{ field.help_text }}</small>{% endif %}
                                {% for error in field.errors %}
                                    <div class="text-danger small">{{ error }}</div>
                                {% endfor %}
                            </div>
                            {% endif %}
                        {% endfor %}
            <button type="submit" class="btn btn-primary mt-2">Créer</button>
            </form>
        </div>
    </div>
</div>
<script src="{% static 'prets/js/debug.js' %}"></script>
<script>
// Autocomplete AJAX pour le champ employé
document.addEventListener('DOMContentLoaded', function() {
    const input = document.getElementById('employe-search');
    const suggestions = document.getElementById('employe-suggestions');
    let timer = null;
    input.addEventListener('input', function() {
        clearTimeout(timer);
        const query = this.value.trim();
        if (query.length < 2) {
            suggestions.innerHTML = '';
            suggestions.style.display = 'none';
            return;
        }
        timer = setTimeout(function() {
            fetch('/sirh/api/employes/?q=' + encodeURIComponent(query))
                .then(r => r.json())
                .then(data => {
                    suggestions.innerHTML = '';
                    if (Array.isArray(data) && data.length > 0) {
                        data.forEach(emp => {
                            const item = document.createElement('button');
                            item.type = 'button';
                            item.className = 'list-group-item list-group-item-action';
                            item.textContent = emp.nom + ' ' + emp.prenom + ' (' + emp.matricule + ')';
                            item.dataset.id = emp.id;
                            item.onclick = function() {
                                input.value = emp.id;
                                suggestions.innerHTML = '';
                                suggestions.style.display = 'none';
                            };
                            suggestions.appendChild(item);
                        });
                        suggestions.style.display = 'block';
                    } else {
                        suggestions.style.display = 'none';
                    }
                });
        }, 250);
    });
    // Cacher suggestions si clic ailleurs
    document.addEventListener('click', function(e) {
        if (!suggestions.contains(e.target) && e.target !== input) {
            suggestions.innerHTML = '';
            suggestions.style.display = 'none';
        }
    });
});
</script>

{% endblock %}
