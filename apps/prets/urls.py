from django.urls import path
from . import views

app_name = "prets"

urlpatterns = [
    path('', views.LoanListView.as_view(), name='loan_list'),
    path('creer/', views.LoanCreateView.as_view(), name='loan_create'),
    path('simuler/', views.LoanSimulateView.as_view(), name='loan_simulate'),
    path('<str:pk>/', views.LoanDetailView.as_view(), name='loan_detail'),
    path('<str:pk>/remboursement/', views.LoanRepayView.as_view(), name='loan_repay'),
    path('<str:pk>/statut/', views.LoanStatusUpdateView.as_view(), name='loan_status_update'),
    path('<str:pk>/echeances/', views.LoanScheduleView.as_view(), name='loan_schedule'),
]
