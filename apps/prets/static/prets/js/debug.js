// JS de debug pour la gestion des prêts
window.addEventListener('DOMContentLoaded', function() {
    console.log('[PRETS] Page chargée:', window.location.pathname);
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function(e) {
            console.log('[PRETS] Formulaire soumis:', form.action || window.location.pathname);
        });
    });
    // Affiche les erreurs Django dans la console si présentes
    const djangoMessages = document.querySelectorAll('.alert');
    djangoMessages.forEach(msg => {
        if (msg.classList.contains('alert-danger') || msg.classList.contains('alert-error')) {
            console.error('[PRETS][Erreur]', msg.textContent.trim());
        } else {
            console.info('[PRETS][Info]', msg.textContent.trim());
        }
    });
});
