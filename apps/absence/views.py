from django.shortcuts import render
from django.http import JsonResponse
from django.views import View
from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import json
import logging

from .services import (
    list_absences, get_absence_detail, create_absence, update_absence,
    approve_absence, reject_absence, delete_absence, list_employes,
    TYPES_ABSENCE, STATUTS_ABSENCE, AbsenceAPIError
)

logger = logging.getLogger(__name__)


class AbsenceListView(LoginRequiredMixin, TemplateView):
    """Vue principale pour afficher la liste des absences"""
    template_name = 'absence/absences_list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'types_absence': TYPES_ABSENCE,
            'statuts_absence': STATUTS_ABSENCE,
            'types_absence_json': json.dumps(TYPES_ABSENCE),
            'statuts_absence_json': json.dumps(STATUTS_ABSENCE),
        })
        return context


@method_decorator(csrf_exempt, name='dispatch')
class AbsenceListApiView(LoginRequiredMixin, View):
    """API pour récupérer la liste des absences avec filtres"""

    def get(self, request):
        try:
            # Récupération des paramètres de filtrage
            employe_id = request.GET.get('employe_id')
            date_debut = request.GET.get('date_debut')
            date_fin = request.GET.get('date_fin')
            type_absence = request.GET.get('type_absence')
            statut = request.GET.get('statut')

            # Appel à l'API SIRH
            result = list_absences(employe_id, date_debut, date_fin)

            # Filtrage côté client pour les paramètres non supportés par l'API
            absences = result.get('data', [])
            if type_absence:
                absences = [a for a in absences if a.get('type_absence') == type_absence]
            if statut:
                absences = [a for a in absences if a.get('statut') == statut]

            # Enrichissement des données
            for absence in absences:
                absence['type_absence_label'] = TYPES_ABSENCE.get(
                    absence.get('type_absence'),
                    absence.get('type_absence', 'Inconnu')
                )
                absence['statut_label'] = STATUTS_ABSENCE.get(
                    absence.get('statut'),
                    absence.get('statut', 'Inconnu')
                )

            return JsonResponse({
                'success': True,
                'data': absences,
                'total': len(absences)
            })

        except AbsenceAPIError as e:
            logger.error(f"Erreur API lors de la récupération des absences: {e}")
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=500)
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des absences: {e}")
            return JsonResponse({
                'success': False,
                'error': 'Erreur interne du serveur'
            }, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class AbsenceDetailApiView(LoginRequiredMixin, View):
    """API pour récupérer le détail d'une absence"""

    def get(self, request, absence_id):
        try:
            result = get_absence_detail(absence_id)

            # Enrichissement des données
            if 'data' in result:
                absence = result['data']
                absence['type_absence_label'] = TYPES_ABSENCE.get(
                    absence.get('type_absence'),
                    absence.get('type_absence', 'Inconnu')
                )
                absence['statut_label'] = STATUTS_ABSENCE.get(
                    absence.get('statut'),
                    absence.get('statut', 'Inconnu')
                )

            return JsonResponse(result)

        except AbsenceAPIError as e:
            logger.error(f"Erreur API lors de la récupération du détail: {e}")
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=500)
        except Exception as e:
            logger.error(f"Erreur lors de la récupération du détail: {e}")
            return JsonResponse({
                'success': False,
                'error': 'Erreur interne du serveur'
            }, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class AbsenceCreateApiView(LoginRequiredMixin, View):
    """API pour créer une nouvelle absence"""

    def post(self, request):
        try:
            data = json.loads(request.body)

            # Validation des champs requis
            required_fields = ['employe_id', 'date_debut', 'date_fin', 'type_absence']
            for field in required_fields:
                if not data.get(field):
                    return JsonResponse({
                        'success': False,
                        'error': f'Le champ {field} est requis'
                    }, status=400)

            # Appel à l'API SIRH
            result = create_absence(data)

            return JsonResponse({
                'success': True,
                'data': result,
                'message': 'Absence créée avec succès'
            })

        except AbsenceAPIError as e:
            logger.error(f"Erreur API lors de la création: {e}")
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=500)
        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'error': 'Données JSON invalides'
            }, status=400)
        except Exception as e:
            logger.error(f"Erreur lors de la création: {e}")
            return JsonResponse({
                'success': False,
                'error': 'Erreur interne du serveur'
            }, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class AbsenceUpdateApiView(LoginRequiredMixin, View):
    """API pour modifier une absence"""

    def put(self, request, absence_id):
        try:
            data = json.loads(request.body)

            # Appel à l'API SIRH
            result = update_absence(absence_id, data)

            return JsonResponse({
                'success': True,
                'data': result,
                'message': 'Absence modifiée avec succès'
            })

        except AbsenceAPIError as e:
            logger.error(f"Erreur API lors de la modification: {e}")
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=500)
        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'error': 'Données JSON invalides'
            }, status=400)
        except Exception as e:
            logger.error(f"Erreur lors de la modification: {e}")
            return JsonResponse({
                'success': False,
                'error': 'Erreur interne du serveur'
            }, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class AbsenceActionApiView(LoginRequiredMixin, View):
    """API pour les actions sur les absences (approuver, rejeter, supprimer)"""

    def post(self, request, absence_id, action):
        try:
            data = json.loads(request.body) if request.body else {}

            if action == 'approve':
                result = approve_absence(absence_id, data)
                message = 'Absence approuvée avec succès'
            elif action == 'reject':
                motif = data.get('motif')
                if not motif:
                    return JsonResponse({
                        'success': False,
                        'error': 'Le motif de rejet est requis'
                    }, status=400)
                result = reject_absence(absence_id, motif, data.get('rejete_par'))
                message = 'Absence rejetée avec succès'
            else:
                return JsonResponse({
                    'success': False,
                    'error': f'Action non supportée: {action}'
                }, status=400)

            return JsonResponse({
                'success': True,
                'data': result,
                'message': message
            })

        except AbsenceAPIError as e:
            logger.error(f"Erreur API lors de l'action {action}: {e}")
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=500)
        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'error': 'Données JSON invalides'
            }, status=400)
        except Exception as e:
            logger.error(f"Erreur lors de l'action {action}: {e}")
            return JsonResponse({
                'success': False,
                'error': 'Erreur interne du serveur'
            }, status=500)

    def delete(self, request, absence_id, action=None):
        """Suppression d'une absence"""
        try:
            result = delete_absence(absence_id)

            return JsonResponse({
                'success': True,
                'data': result,
                'message': 'Absence supprimée avec succès'
            })

        except AbsenceAPIError as e:
            logger.error(f"Erreur API lors de la suppression: {e}")
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=500)
        except Exception as e:
            logger.error(f"Erreur lors de la suppression: {e}")
            return JsonResponse({
                'success': False,
                'error': 'Erreur interne du serveur'
            }, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class EmployeListApiView(LoginRequiredMixin, View):
    """API pour récupérer la liste des employés"""

    def get(self, request):
        try:
            result = list_employes()

            return JsonResponse(result)

        except AbsenceAPIError as e:
            logger.error(f"Erreur API lors de la récupération des employés: {e}")
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=500)
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des employés: {e}")
            return JsonResponse({
                'success': False,
                'error': 'Erreur interne du serveur'
            }, status=500)
