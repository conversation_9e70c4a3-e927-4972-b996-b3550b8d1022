/**
 * Gestion des absences - Interface utilisateur
 * Utilise jQuery et Bootstrap pour les interactions
 */
class AbsenceManager {
    constructor() {
        this.absences = [];
        this.employes = [];
        this.currentSort = { field: null, direction: 'asc' };
        this.filters = {
            employe_id: '',
            date_debut: '',
            date_fin: '',
            type_absence: '',
            statut: '',
            search: ''
        };
        
    this.init();
    }

    init() {
        this.bindEvents();
        this.loadEmployes();
        this.loadAbsences();
        this.restoreFilters();
        
        // Auto-refresh toutes les 30 secondes
    setInterval(() => this.loadAbsences(), 30000);
    }

    bindEvents() {
        // Boutons principaux
        $('#btn-nouvelle-absence').on('click', () => this.showAbsenceForm());
        $('#btn-refresh').on('click', () => this.loadAbsences());
        $('#btn-reset-filters').on('click', () => this.resetFilters());

        // Filtres
        $('#filter-employe, #filter-type, #filter-statut').on('change', () => this.applyFilters());
        $('#filter-date-debut, #filter-date-fin').on('change', () => this.applyFilters());
        
        // Recherche avec debouncing
        let searchTimeout;
        $('#search-input').on('input', () => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => this.applyFilters(), 300);
        });

        // Tri des colonnes
        $('.sortable').on('click', (e) => this.handleSort(e));

        // Formulaire d'absence
        $('#btn-save-absence').on('click', () => this.saveAbsence());
        $('#btn-delete-absence').on('click', () => this.showDeleteConfirmation());

        // Actions d'approbation/rejet
        $('#btn-confirm-approve').on('click', () => this.approveAbsence());
        $('#btn-confirm-reject').on('click', () => this.rejectAbsence());
        $('#btn-confirm-delete').on('click', () => this.deleteAbsence());

        // Raccourcis clavier
        $(document).on('keydown', (e) => {
            if (e.ctrlKey && e.key === 'n') {
                e.preventDefault();
                this.showAbsenceForm();
            }
        });
    }

    async loadEmployes() {
        try {
            const response = await fetch(window.ABSENCE_CONFIG.urls.employes);
            const result = await response.json();
            
            if (result.success) {
                this.employes = result.data || [];
                this.populateEmployeSelect();
            } else {
                this.showToast('Erreur lors du chargement des employés', 'error');
            }
        } catch (error) {
            console.error('Erreur lors du chargement des employés:', error);
            this.showToast('Erreur de connexion', 'error');
        }
    }

    populateEmployeSelect() {
        const selects = ['#filter-employe', '#employe-select'];
        
        selects.forEach(selector => {
            const $select = $(selector);
            const currentValue = $select.val();
            
            // Garder la première option
            const firstOption = $select.find('option:first');
            $select.empty().append(firstOption);
            
            this.employes.forEach(employe => {
                const option = new Option(
                    `${employe.nom} ${employe.prenom}`,
                    employe.id
                );
                $select.append(option);
            });
            
            // Restaurer la valeur sélectionnée
            if (currentValue) {
                $select.val(currentValue);
            }
        });
    }

    async loadAbsences() {
        this.showLoading(true);
        
        try {
            const params = new URLSearchParams();
            
            if (this.filters.employe_id) params.append('employe_id', this.filters.employe_id);
            if (this.filters.date_debut) params.append('date_debut', this.filters.date_debut);
            if (this.filters.date_fin) params.append('date_fin', this.filters.date_fin);
            if (this.filters.type_absence) params.append('type_absence', this.filters.type_absence);
            if (this.filters.statut) params.append('statut', this.filters.statut);

            const url = `${window.ABSENCE_CONFIG.urls.list}?${params.toString()}`;
            const response = await fetch(url);
            const result = await response.json();
            
            if (result.success) {
                this.absences = result.data || [];
                this.renderAbsences();
                this.updateCounters();
            } else {
                this.showToast(result.error || 'Erreur lors du chargement', 'error');
            }
        } catch (error) {
            console.error('Erreur lors du chargement des absences:', error);
            this.showToast('Erreur de connexion', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    renderAbsences() {
        const $tbody = $('#absences-tbody');
        const $noData = $('#no-data-message');
        
        // Appliquer les filtres côté client
        let filteredAbsences = this.applyClientFilters(this.absences);
        
        // Appliquer le tri
        if (this.currentSort.field) {
            filteredAbsences = this.sortAbsences(filteredAbsences);
        }

        if (filteredAbsences.length === 0) {
            $tbody.empty();
            $noData.show();
            return;
        }

        $noData.hide();
        
        const rows = filteredAbsences.map(absence => this.createAbsenceRow(absence));
        $tbody.html(rows.join(''));
        
        // Bind events pour les actions
        this.bindRowEvents();
    }

    createAbsenceRow(absence) {
        // Adapter les noms de champs pour correspondre à l'API (camelCase)
        const employeId = absence.employeId || absence.employe_id;
        const employe = this.employes.find(e => e.id === employeId);
        const employeNom = employe ? `${employe.nom} ${employe.prenom}` : 'Inconnu';
        const dateDebut = absence.dateDebut || absence.date_debut;
        const dateFin = absence.dateFin || absence.date_fin;
        const typeAbsence = absence.typeAbsence || absence.type_absence;
        const typeAbsenceLabel = absence.type_absence_label || this.getTypeAbsenceLabel(typeAbsence);
        const statut = absence.statut;
        const statutLabel = absence.statut_label || this.getStatutLabel(statut);
        const motif = absence.motif || '-';
        const actions = this.getActionButtons(absence);
        const statusBadge = this.getStatusBadge(statutLabel);
        return `
            <tr data-absence-id="${absence.id}">
                <td>${employeNom}</td>
                <td>${this.formatDate(dateDebut)}</td>
                <td>${this.formatDate(dateFin)}</td>
                <td>${typeAbsenceLabel || typeAbsence || 'Inconnu'}</td>
                <td>${statusBadge}</td>
                <td>
                    <span class="text-truncate d-inline-block" style="max-width: 200px;" 
                          title="${motif}">${motif}</span>
                </td>
                <td>${actions}</td>
            </tr>
        `;
    }

    // Ajout utilitaire pour label type absence
    getTypeAbsenceLabel(type) {
        if (!type) return 'Inconnu';
        const map = window.ABSENCE_CONFIG?.types_absence || {};
        return map[type] || type;
    }

    // Ajout utilitaire pour label statut
    getStatutLabel(statut) {
        if (!statut) return 'Inconnu';
        const map = window.ABSENCE_CONFIG?.statuts_absence || {};
        return map[statut] || statut;
    }

    getStatusBadge(statut) {
        const badges = {
            'EN_ATTENTE': '<span class="badge bg-warning">En attente</span>',
            'APPROUVEE': '<span class="badge bg-success">Approuvée</span>',
            'REJETEE': '<span class="badge bg-danger">Rejetée</span>',
            'ANNULEE': '<span class="badge bg-secondary">Annulée</span>'
        };
        
        return badges[statut] || `<span class="badge bg-secondary">${statut}</span>`;
    }

    getActionButtons(absence) {
        const canEdit = absence.statut === 'EN_ATTENTE';
        const canApprove = absence.statut === 'EN_ATTENTE';
        
        let buttons = `
            <button class="btn btn-sm btn-outline-primary me-1" onclick="absenceManager.showAbsenceDetail('${absence.id}')" 
                    title="Voir détails">
                <i class="bi bi-eye"></i>
            </button>
        `;
        
        if (canEdit) {
            buttons += `
                <button class="btn btn-sm btn-outline-secondary me-1" onclick="absenceManager.editAbsence('${absence.id}')" 
                        title="Modifier">
                    <i class="bi bi-pencil"></i>
                </button>
            `;
        }
        
        if (canApprove) {
            buttons += `
                <button class="btn btn-sm btn-outline-success me-1" onclick="absenceManager.showApproveModal('${absence.id}')" 
                        title="Approuver">
                    <i class="bi bi-check"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="absenceManager.showRejectModal('${absence.id}')" 
                        title="Rejeter">
                    <i class="bi bi-x"></i>
                </button>
            `;
        }
        
        return buttons;
    }

    applyClientFilters(absences) {
        return absences.filter(absence => {
            // Filtre de recherche
            if (this.filters.search) {
                const searchTerm = this.filters.search.toLowerCase();
                const employe = this.employes.find(e => e.id === absence.employe_id);
                const employeNom = employe ? `${employe.nom} ${employe.prenom}`.toLowerCase() : '';
                const motif = (absence.motif || '').toLowerCase();
                const type = (absence.type_absence_label || absence.type_absence || '').toLowerCase();
                
                if (!employeNom.includes(searchTerm) && 
                    !motif.includes(searchTerm) && 
                    !type.includes(searchTerm)) {
                    return false;
                }
            }
            
            return true;
        });
    }

    sortAbsences(absences) {
        return absences.sort((a, b) => {
            let aVal = a[this.currentSort.field];
            let bVal = b[this.currentSort.field];
            
            // Gestion spéciale pour le nom d'employé
            if (this.currentSort.field === 'employe_nom') {
                const employeA = this.employes.find(e => e.id === a.employe_id);
                const employeB = this.employes.find(e => e.id === b.employe_id);
                aVal = employeA ? `${employeA.nom} ${employeA.prenom}` : '';
                bVal = employeB ? `${employeB.nom} ${employeB.prenom}` : '';
            }
            
            if (aVal < bVal) return this.currentSort.direction === 'asc' ? -1 : 1;
            if (aVal > bVal) return this.currentSort.direction === 'asc' ? 1 : -1;
            return 0;
        });
    }

    handleSort(e) {
        const field = $(e.currentTarget).data('sort');
        
        if (this.currentSort.field === field) {
            this.currentSort.direction = this.currentSort.direction === 'asc' ? 'desc' : 'asc';
        } else {
            this.currentSort.field = field;
            this.currentSort.direction = 'asc';
        }
        
        // Mettre à jour les icônes de tri
        $('.sortable i').removeClass('bi-arrow-up bi-arrow-down').addClass('bi-arrow-down-up');
        const icon = this.currentSort.direction === 'asc' ? 'bi-arrow-up' : 'bi-arrow-down';
        $(e.currentTarget).find('i').removeClass('bi-arrow-down-up').addClass(icon);
        
        this.renderAbsences();
    }

    updateCounters() {
        const total = this.absences.length;
        const enAttente = this.absences.filter(a => a.statut === 'EN_ATTENTE').length;
        const approuvees = this.absences.filter(a => a.statut === 'APPROUVEE').length;
        const rejetees = this.absences.filter(a => a.statut === 'REJETEE').length;
        
        $('#total-absences').text(total);
        $('#en-attente').text(enAttente);
        $('#approuvees').text(approuvees);
        $('#rejetees').text(rejetees);
    }

    applyFilters() {
        this.filters.employe_id = $('#filter-employe').val();
        this.filters.date_debut = $('#filter-date-debut').val();
        this.filters.date_fin = $('#filter-date-fin').val();
        this.filters.type_absence = $('#filter-type').val();
        this.filters.statut = $('#filter-statut').val();
        this.filters.search = $('#search-input').val();
        
        this.saveFilters();
        this.loadAbsences();
    }

    resetFilters() {
        this.filters = {
            employe_id: '',
            date_debut: '',
            date_fin: '',
            type_absence: '',
            statut: '',
            search: ''
        };
        
        $('#filter-employe').val('');
        $('#filter-date-debut').val('');
        $('#filter-date-fin').val('');
        $('#filter-type').val('');
        $('#filter-statut').val('');
        $('#search-input').val('');
        
        this.saveFilters();
        this.loadAbsences();
    }

    saveFilters() {
        localStorage.setItem('absence_filters', JSON.stringify(this.filters));
    }

    restoreFilters() {
        const saved = localStorage.getItem('absence_filters');
        if (saved) {
            this.filters = { ...this.filters, ...JSON.parse(saved) };
            
            $('#filter-employe').val(this.filters.employe_id);
            $('#filter-date-debut').val(this.filters.date_debut);
            $('#filter-date-fin').val(this.filters.date_fin);
            $('#filter-type').val(this.filters.type_absence);
            $('#filter-statut').val(this.filters.statut);
            $('#search-input').val(this.filters.search);
        }
    }

    showLoading(show) {
        if (show) {
            $('#loading-spinner').show();
            $('#absences-table').hide();
            $('#no-data-message').hide();
        } else {
            $('#loading-spinner').hide();
            $('#absences-table').show();
        }
    }

    formatDate(dateStr) {
        if (!dateStr) return '-';
        const date = new Date(dateStr);
        return date.toLocaleDateString('fr-FR');
    }

    showToast(message, type = 'info') {
        const bgClass = {
            'success': 'bg-success',
            'error': 'bg-danger',
            'warning': 'bg-warning',
            'info': 'bg-info'
        }[type] || 'bg-info';
        
        const toast = $(`
            <div class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header ${bgClass} text-white">
                    <strong class="me-auto">Notification</strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `);
        
        $('#toast-container').append(toast);
        const bsToast = new bootstrap.Toast(toast[0]);
        bsToast.show();
        
        // Auto-remove après 5 secondes
        setTimeout(() => toast.remove(), 5000);
    }

    bindRowEvents() {
        // Les événements sont déjà bindés via onclick dans le HTML
        // Cette méthode peut être étendue pour d'autres événements
    }

    // === GESTION DES MODALS ET FORMULAIRES ===

    showAbsenceForm(absenceId = null) {
        const isEdit = !!absenceId;

        // Réinitialiser le formulaire
        $('#absence-form')[0].reset();
        $('#absence-id').val(absenceId || '');

        // Mettre à jour le titre et les boutons
        $('#modal-title').text(isEdit ? 'Modifier l\'absence' : 'Nouvelle absence');
        $('#save-text').text(isEdit ? 'Modifier' : 'Créer');
        $('#btn-delete-absence').toggle(isEdit);

        // Charger les données si modification
        if (isEdit) {
            this.loadAbsenceForEdit(absenceId);
        }

        // Afficher le modal
        const modal = new bootstrap.Modal(document.getElementById('absenceFormModal'));
        modal.show();
    }

    async loadAbsenceForEdit(absenceId) {
        try {
            const url = window.ABSENCE_CONFIG.urls.detail.replace('__ID__', absenceId);
            const response = await fetch(url);
            const result = await response.json();

            if (result.success && result.data) {
                const absence = result.data;

                $('#employe-select').val(absence.employe_id);
                $('#type-absence-select').val(absence.type_absence);
                $('#date-debut').val(absence.date_debut);
                $('#date-fin').val(absence.date_fin);
                $('#motif').val(absence.motif || '');
                $('#impact-paie').prop('checked', absence.impact_paie || false);

                // Déclencher le calcul de durée
                $('#date-debut').trigger('change');
            }
        } catch (error) {
            console.error('Erreur lors du chargement de l\'absence:', error);
            this.showToast('Erreur lors du chargement des données', 'error');
        }
    }

    async saveAbsence() {
        const form = document.getElementById('absence-form');
        const formData = new FormData(form);
        const absenceId = $('#absence-id').val();
        const isEdit = !!absenceId;

        // Validation côté client
        if (!form.checkValidity()) {
            form.classList.add('was-validated');
            return;
        }

        // Préparer les données
        const data = {
            employe_id: formData.get('employe_id'),
            type_absence: formData.get('type_absence'),
            date_debut: formData.get('date_debut'),
            date_fin: formData.get('date_fin'),
            motif: formData.get('motif') || '',
            impact_paie: formData.has('impact_paie')
        };

        // Afficher le loading
        this.toggleSaveButton(true);

        try {
            let url, method;
            if (isEdit) {
                url = window.ABSENCE_CONFIG.urls.update.replace('__ID__', absenceId);
                method = 'PUT';
            } else {
                url = window.ABSENCE_CONFIG.urls.create;
                method = 'POST';
            }

            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCsrfToken()
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                this.showToast(result.message || 'Absence sauvegardée avec succès', 'success');
                bootstrap.Modal.getInstance(document.getElementById('absenceFormModal')).hide();
                this.loadAbsences();
            } else {
                this.showToast(result.error || 'Erreur lors de la sauvegarde', 'error');
            }
        } catch (error) {
            console.error('Erreur lors de la sauvegarde:', error);
            this.showToast('Erreur de connexion', 'error');
        } finally {
            this.toggleSaveButton(false);
        }
    }

    toggleSaveButton(loading) {
        const $btn = $('#btn-save-absence');
        const $spinner = $('#save-spinner');
        const $icon = $('#save-icon');

        if (loading) {
            $btn.prop('disabled', true);
            $spinner.show();
            $icon.hide();
        } else {
            $btn.prop('disabled', false);
            $spinner.hide();
            $icon.show();
        }
    }

    async showAbsenceDetail(absenceId) {
        try {
            const url = window.ABSENCE_CONFIG.urls.detail.replace('__ID__', absenceId);
            const response = await fetch(url);
            const result = await response.json();

            if (result.success && result.data) {
                // Afficher les détails dans un modal ou une page dédiée
                // Pour l'instant, on utilise le modal de modification en lecture seule
                this.showAbsenceForm(absenceId);

                // Désactiver tous les champs
                $('#absence-form input, #absence-form select, #absence-form textarea').prop('disabled', true);
                $('#btn-save-absence').hide();
                $('#modal-title').text('Détails de l\'absence');
            }
        } catch (error) {
            console.error('Erreur lors du chargement du détail:', error);
            this.showToast('Erreur lors du chargement', 'error');
        }
    }

    editAbsence(absenceId) {
        this.showAbsenceForm(absenceId);
    }

    showApproveModal(absenceId) {
        $('#approve-absence-id').val(absenceId);
        this.loadAbsenceDetails(absenceId, 'approve-absence-details');

        const modal = new bootstrap.Modal(document.getElementById('approveModal'));
        modal.show();
    }

    showRejectModal(absenceId) {
        $('#reject-absence-id').val(absenceId);
        $('#reject-reason').val('');
        this.loadAbsenceDetails(absenceId, 'reject-absence-details');

        const modal = new bootstrap.Modal(document.getElementById('rejectModal'));
        modal.show();
    }

    showDeleteConfirmation() {
        const absenceId = $('#absence-id').val();
        $('#delete-absence-id').val(absenceId);
        this.loadAbsenceDetails(absenceId, 'delete-absence-details');

        // Fermer le modal de formulaire
        bootstrap.Modal.getInstance(document.getElementById('absenceFormModal')).hide();

        // Ouvrir le modal de confirmation
        const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
        modal.show();
    }

    async loadAbsenceDetails(absenceId, containerId) {
        try {
            const url = window.ABSENCE_CONFIG.urls.detail.replace('__ID__', absenceId);
            const response = await fetch(url);
            const result = await response.json();

            if (result.success && result.data) {
                const absence = result.data;
                const employe = this.employes.find(e => e.id === absence.employe_id);

                const template = document.getElementById('absence-details-template').innerHTML;
                const $container = $(`#${containerId}`);

                $container.html(template);

                // Remplir les données
                $container.find('.employe-nom').text(employe ? `${employe.nom} ${employe.prenom}` : 'Inconnu');
                $container.find('.type-absence').text(absence.type_absence_label || absence.type_absence);
                $container.find('.date-debut').text(this.formatDate(absence.date_debut));
                $container.find('.date-fin').text(this.formatDate(absence.date_fin));
                $container.find('.impact-paie').text(absence.impact_paie ? 'Oui' : 'Non');

                // Calculer la durée
                const debut = new Date(absence.date_debut);
                const fin = new Date(absence.date_fin);
                const duree = Math.ceil((fin - debut) / (1000 * 60 * 60 * 24)) + 1;
                $container.find('.duree').text(duree);

                // Motif (optionnel)
                if (absence.motif) {
                    $container.find('.motif').text(absence.motif);
                    $container.find('.motif-row').show();
                }
            }
        } catch (error) {
            console.error('Erreur lors du chargement des détails:', error);
        }
    }

    getCsrfToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
    }

    // === ACTIONS D'APPROBATION/REJET/SUPPRESSION ===

    async approveAbsence() {
        const absenceId = $('#approve-absence-id').val();
        const data = {
            approuve_par: $('#approve-by').val(),
            commentaire: $('#approve-comment').val()
        };

        this.toggleActionButton('approve', true);

        try {
            const url = window.ABSENCE_CONFIG.urls.action
                .replace('__ID__', absenceId)
                .replace('__ACTION__', 'approve');

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCsrfToken()
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                this.showToast(result.message || 'Absence approuvée avec succès', 'success');
                bootstrap.Modal.getInstance(document.getElementById('approveModal')).hide();
                this.loadAbsences();
            } else {
                this.showToast(result.error || 'Erreur lors de l\'approbation', 'error');
            }
        } catch (error) {
            console.error('Erreur lors de l\'approbation:', error);
            this.showToast('Erreur de connexion', 'error');
        } finally {
            this.toggleActionButton('approve', false);
        }
    }

    async rejectAbsence() {
        const absenceId = $('#reject-absence-id').val();
        const motif = $('#reject-reason').val().trim();

        if (!motif) {
            $('#reject-reason').addClass('is-invalid');
            return;
        }

        $('#reject-reason').removeClass('is-invalid');

        const data = {
            motif: motif,
            rejete_par: $('#reject-by').val()
        };

        this.toggleActionButton('reject', true);

        try {
            const url = window.ABSENCE_CONFIG.urls.action
                .replace('__ID__', absenceId)
                .replace('__ACTION__', 'reject');

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCsrfToken()
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                this.showToast(result.message || 'Absence rejetée avec succès', 'success');
                bootstrap.Modal.getInstance(document.getElementById('rejectModal')).hide();
                this.loadAbsences();
            } else {
                this.showToast(result.error || 'Erreur lors du rejet', 'error');
            }
        } catch (error) {
            console.error('Erreur lors du rejet:', error);
            this.showToast('Erreur de connexion', 'error');
        } finally {
            this.toggleActionButton('reject', false);
        }
    }

    async deleteAbsence() {
        const absenceId = $('#delete-absence-id').val();

        this.toggleActionButton('delete', true);

        try {
            const url = window.ABSENCE_CONFIG.urls.delete.replace('__ID__', absenceId);

            const response = await fetch(url, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': this.getCsrfToken()
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showToast(result.message || 'Absence supprimée avec succès', 'success');
                bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
                this.loadAbsences();
            } else {
                this.showToast(result.error || 'Erreur lors de la suppression', 'error');
            }
        } catch (error) {
            console.error('Erreur lors de la suppression:', error);
            this.showToast('Erreur de connexion', 'error');
        } finally {
            this.toggleActionButton('delete', false);
        }
    }

    toggleActionButton(action, loading) {
        const $btn = $(`#btn-confirm-${action}`);
        const $spinner = $(`#${action}-spinner`);
        const $icon = $(`#${action}-icon`);

        if (loading) {
            $btn.prop('disabled', true);
            $spinner.show();
            $icon.hide();
        } else {
            $btn.prop('disabled', false);
            $spinner.hide();
            $icon.show();
        }
    }

    // === UTILITAIRES ===

    calculateBusinessDays(startDate, endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        let businessDays = 0;

        for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
            if (d.getDay() !== 0 && d.getDay() !== 6) { // Pas dimanche (0) ni samedi (6)
                businessDays++;
            }
        }

        return businessDays;
    }

    isWeekend(date) {
        const day = new Date(date).getDay();
        return day === 0 || day === 6;
    }

    validateDateRange(startDate, endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const errors = [];

        if (start < today) {
            errors.push('La date de début ne peut pas être dans le passé');
        }

        if (end < start) {
            errors.push('La date de fin doit être postérieure à la date de début');
        }

        const diffDays = Math.ceil((end - start) / (1000 * 60 * 60 * 24)) + 1;
        if (diffDays > 365) {
            errors.push('La durée de l\'absence ne peut pas dépasser 365 jours');
        }

        return errors;
    }

    exportToCSV() {
        const headers = ['Employé', 'Date début', 'Date fin', 'Type', 'Statut', 'Motif', 'Impact paie'];
        const rows = this.absences.map(absence => {
            const employe = this.employes.find(e => e.id === absence.employe_id);
            const employeNom = employe ? `${employe.nom} ${employe.prenom}` : 'Inconnu';

            return [
                employeNom,
                this.formatDate(absence.date_debut),
                this.formatDate(absence.date_fin),
                absence.type_absence_label || absence.type_absence,
                absence.statut_label || absence.statut,
                absence.motif || '',
                absence.impact_paie ? 'Oui' : 'Non'
            ];
        });

        const csvContent = [headers, ...rows]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `absences_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// Initialisation globale
let absenceManager;

$(document).ready(function() {
    absenceManager = new AbsenceManager();
});
