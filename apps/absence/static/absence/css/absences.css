/* Styles personnalisés pour la gestion des absences */

/* Amélioration des compteurs */
.card.bg-primary,
.card.bg-warning,
.card.bg-success,
.card.bg-danger {
    transition: transform 0.2s ease-in-out;
}

.card.bg-primary:hover,
.card.bg-warning:hover,
.card.bg-success:hover,
.card.bg-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Amélioration du tableau */
.table th.sortable {
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s ease;
}

.table th.sortable:hover {
    background-color: #e9ecef !important;
}

.table th.sortable i {
    opacity: 0.5;
    transition: opacity 0.2s ease;
}

.table th.sortable:hover i {
    opacity: 1;
}

/* Badges de statut */
.badge {
    font-size: 0.75em;
    padding: 0.375rem 0.75rem;
}

/* Boutons d'action */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Loading spinner */
#loading-spinner {
    min-height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* Message vide */
#no-data-message {
    min-height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* Amélioration des modals */
.modal-header.bg-success,
.modal-header.bg-danger {
    border-bottom: none;
}

.modal-header .btn-close-white {
    filter: brightness(0) invert(1);
}

/* Formulaire d'absence */
.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.invalid-feedback {
    display: block;
}

/* Compteur de caractères */
.form-text {
    font-size: 0.875em;
}

.text-warning {
    color: #ffc107 !important;
}

.text-danger {
    color: #dc3545 !important;
}

/* Alertes dans les modals */
.alert {
    border: none;
    border-radius: 0.5rem;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

/* Détails d'absence */
.card-body .row {
    margin-bottom: 0.5rem;
}

.card-body .row:last-child {
    margin-bottom: 0;
}

/* Responsive */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .btn-group-sm > .btn,
    .btn-sm {
        padding: 0.125rem 0.25rem;
        font-size: 0.75rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .modal-dialog {
        margin: 0.5rem;
    }
}

/* Animation pour les toasts */
.toast {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Amélioration des filtres */
.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.form-select:focus,
.form-control:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Indicateurs visuels */
.text-truncate {
    max-width: 200px;
}

/* Amélioration des boutons */
.btn {
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn:active {
    transform: translateY(0);
}

/* Spinner dans les boutons */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Tooltips personnalisés */
.tooltip {
    font-size: 0.875rem;
}

/* Amélioration de l'accessibilité */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid #0d6efd;
    outline-offset: 2px;
}

/* États de validation */
.was-validated .form-control:valid {
    border-color: #198754;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 1.38 1.38 3.22-**********-4.16 4.16z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-control:invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4 1.4-1.4M8.6 7.4 7.2 6 5.8 7.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Amélioration des cartes de compteurs */
.card .fs-1 {
    opacity: 0.8;
}

/* Styles pour les actions rapides */
.btn-group-sm .btn {
    margin-right: 0.25rem;
}

.btn-group-sm .btn:last-child {
    margin-right: 0;
}

/* Amélioration de la lisibilité */
.table td {
    vertical-align: middle;
}

.table th {
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

/* Styles pour les états de chargement */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Amélioration des modals sur mobile */
@media (max-width: 576px) {
    .modal-dialog {
        margin: 0;
        height: 100vh;
        max-width: 100%;
    }
    
    .modal-content {
        height: 100vh;
        border-radius: 0;
    }
    
    .modal-body {
        overflow-y: auto;
    }
}
