from django.urls import path
from .views import (
    AbsenceListView,
    AbsenceListApiView,
    AbsenceDetailApiView,
    AbsenceCreateApiView,
    AbsenceUpdateApiView,
    AbsenceActionApiView,
    EmployeListApiView,
)

app_name = "absence"

urlpatterns = [
    # Interface utilisateur
    path('', AbsenceListView.as_view(), name='absence_list'),
    
    # API endpoints
    path('api/absences/', AbsenceListApiView.as_view(), name='api_absence_list'),
    path('api/absences/create/', AbsenceCreateApiView.as_view(), name='api_absence_create'),
    path('api/absences/<str:absence_id>/', AbsenceDetailApiView.as_view(), name='api_absence_detail'),
    path('api/absences/<str:absence_id>/update/', AbsenceUpdateApiView.as_view(), name='api_absence_update'),
    path('api/absences/<str:absence_id>/<str:action>/', AbsenceActionApiView.as_view(), name='api_absence_action'),
    path('api/absences/<str:absence_id>/delete/', AbsenceActionApiView.as_view(), name='api_absence_delete'),
    
    # API employés
    path('api/employes/', EmployeListApiView.as_view(), name='api_employe_list'),
]
