import requests
from typing import Optional, Dict, Any, List

# URL de base de l'API SIRH (à adapter selon l'environnement)
API_BASE_URL = "http://localhost:3000"

class AbsenceAPIError(Exception):
    """Exception personnalisée pour les erreurs d'appel à l'API Absence."""
    pass

def list_absences(employe_id: Optional[str] = None, date_debut: Optional[str] = None, date_fin: Optional[str] = None) -> Dict[str, Any]:
    """
    Récupère la liste des absences via l'API.
    :param employe_id: UUID de l'employé (optionnel)
    :param date_debut: Date de début (YYYY-MM-DD, optionnel)
    :param date_fin: Date de fin (YYYY-MM-DD, optionnel)
    :return: Réponse complète de l'API avec métadonnées
    """
    params = {}
    if employe_id:
        params['employeId'] = employe_id
    if date_debut:
        params['dateDebut'] = date_debut
    if date_fin:
        params['dateFin'] = date_fin
    url = f"{API_BASE_URL}/api/absences"
    try:
        resp = requests.get(url, params=params)
        resp.raise_for_status()
        return resp.json()
    except Exception as e:
        raise AbsenceAPIError(f"Erreur lors de la récupération des absences: {e}")

def get_absence_detail(absence_id: str) -> Dict[str, Any]:
    """
    Récupère le détail d'une absence via l'API.
    :param absence_id: ID de l'absence
    :return: Détail de l'absence
    """
    url = f"{API_BASE_URL}/api/absences/{absence_id}"
    try:
        resp = requests.get(url)
        resp.raise_for_status()
        return resp.json()
    except Exception as e:
        raise AbsenceAPIError(f"Erreur lors de la récupération du détail de l'absence: {e}")

def create_absence(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Crée une nouvelle absence via l'API.
    :param data: Dictionnaire avec les champs requis (voir doc API)
    :return: Absence créée (dict)
    """
    url = f"{API_BASE_URL}/api/absences"
    try:
        resp = requests.post(url, json=data)
        resp.raise_for_status()
        return resp.json()
    except Exception as e:
        raise AbsenceAPIError(f"Erreur lors de la création de l'absence: {e}")

def update_absence(absence_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Met à jour une absence via l'API.
    :param absence_id: ID de l'absence à modifier
    :param data: Données à mettre à jour
    :return: Absence modifiée
    """
    url = f"{API_BASE_URL}/api/absences/{absence_id}"
    try:
        resp = requests.put(url, json=data)
        resp.raise_for_status()
        return resp.json()
    except Exception as e:
        raise AbsenceAPIError(f"Erreur lors de la modification de l'absence: {e}")

def approve_absence(absence_id: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Approuve une absence via l'API.
    :param absence_id: ID de l'absence à approuver
    :param data: Données optionnelles (approuvé par, commentaire)
    :return: Réponse de l'API
    """
    url = f"{API_BASE_URL}/api/absences/{absence_id}/approuver"
    try:
        resp = requests.post(url, json=data or {})
        resp.raise_for_status()
        return resp.json()
    except Exception as e:
        raise AbsenceAPIError(f"Erreur lors de l'approbation de l'absence: {e}")

def reject_absence(absence_id: str, motif: str, rejete_par: Optional[str] = None) -> Dict[str, Any]:
    """
    Rejette une absence via l'API.
    :param absence_id: ID de l'absence à rejeter
    :param motif: Motif du rejet (obligatoire)
    :param rejete_par: Nom/ID de la personne qui rejette (optionnel)
    :return: Réponse de l'API
    """
    url = f"{API_BASE_URL}/api/absences/{absence_id}/rejeter"
    data = {"motif": motif}
    if rejete_par:
        data["rejete_par"] = rejete_par
    try:
        resp = requests.post(url, json=data)
        resp.raise_for_status()
        return resp.json()
    except Exception as e:
        raise AbsenceAPIError(f"Erreur lors du rejet de l'absence: {e}")

def delete_absence(absence_id: str) -> Dict[str, Any]:
    """
    Supprime une absence via l'API.
    :param absence_id: ID de l'absence à supprimer
    :return: Réponse de l'API
    """
    url = f"{API_BASE_URL}/api/absences/{absence_id}"
    try:
        resp = requests.delete(url)
        resp.raise_for_status()
        return resp.json()
    except Exception as e:
        raise AbsenceAPIError(f"Erreur lors de la suppression de l'absence: {e}")

def list_employes() -> Dict[str, Any]:
    """
    Récupère la liste des employés via l'API.
    :return: Liste des employés
    """
    url = f"{API_BASE_URL}/api/employes"
    try:
        resp = requests.get(url)
        resp.raise_for_status()
        return resp.json()
    except Exception as e:
        raise AbsenceAPIError(f"Erreur lors de la récupération des employés: {e}")

# Types d'absence disponibles (selon la documentation API)
TYPES_ABSENCE = {
    'CONGE_PAYE': 'Congé payé',
    'CONGE_SANS_SOLDE': 'Congé sans solde',
    'MALADIE': 'Arrêt maladie',
    'FORMATION': 'Formation',
    'MATERNITE': 'Congé maternité',
    'PATERNITE': 'Congé paternité',
    'AUTRE': 'Autre'
}

# Statuts d'absence possibles
STATUTS_ABSENCE = {
    'EN_ATTENTE': 'En attente',
    'APPROUVEE': 'Approuvée',
    'REJETEE': 'Rejetée',
    'ANNULEE': 'Annulée'
}
