<!-- Modal pour créer/modifier une absence -->
<div class="modal fade" id="absenceFormModal" tabindex="-1" aria-labelledby="absenceFormModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="absenceFormModalLabel">
                    <i class="bi bi-calendar-plus me-2"></i>
                    <span id="modal-title">Nouvelle Absence</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Alertes -->
                <div id="form-alerts"></div>

                <form id="absence-form">
                    <input type="hidden" id="absence-id" name="absence_id">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="employe-select" class="form-label">
                                    Employé <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="employe-select" name="employe_id" required>
                                    <option value="">Sélectionner un employé</option>
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="type-absence-select" class="form-label">
                                    Type d'absence <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="type-absence-select" name="type_absence" required>
                                    <option value="">Sélectionner un type</option>
                                    {% for code, label in types_absence.items %}
                                    <option value="{{ code }}">{{ label }}</option>
                                    {% endfor %}
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="date-debut" class="form-label">
                                    Date de début <span class="text-danger">*</span>
                                </label>
                                <input type="date" class="form-control" id="date-debut" name="date_debut" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="date-fin" class="form-label">
                                    Date de fin <span class="text-danger">*</span>
                                </label>
                                <input type="date" class="form-control" id="date-fin" name="date_fin" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="motif" class="form-label">Motif</label>
                        <textarea class="form-control" id="motif" name="motif" rows="3" 
                                  placeholder="Décrivez le motif de l'absence..." maxlength="500"></textarea>
                        <div class="form-text">
                            <span id="motif-count">0</span>/500 caractères
                        </div>
                        <div class="invalid-feedback"></div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="impact-paie" name="impact_paie">
                            <label class="form-check-label" for="impact-paie">
                                Impact sur la paie
                                <i class="bi bi-info-circle ms-1" data-bs-toggle="tooltip" 
                                   title="Cochez cette case si cette absence doit affecter le calcul de la paie"></i>
                            </label>
                        </div>
                    </div>

                    <!-- Informations calculées -->
                    <div class="row" id="absence-info" style="display: none;">
                        <div class="col-md-6">
                            <div class="alert alert-info">
                                <strong>Durée:</strong> <span id="duree-absence">0</span> jour(s)
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-warning" id="weekend-warning" style="display: none;">
                                <i class="bi bi-exclamation-triangle me-1"></i>
                                Période incluant des week-ends
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-1"></i>
                    Annuler
                </button>
                <button type="button" class="btn btn-danger" id="btn-delete-absence" style="display: none;">
                    <i class="bi bi-trash me-1"></i>
                    Supprimer
                </button>
                <button type="button" class="btn btn-primary" id="btn-save-absence">
                    <span class="spinner-border spinner-border-sm me-2" id="save-spinner" style="display: none;"></span>
                    <i class="bi bi-check-circle me-1" id="save-icon"></i>
                    <span id="save-text">Enregistrer</span>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Validation et calculs côté client pour le formulaire
document.addEventListener('DOMContentLoaded', function() {
    const dateDebut = document.getElementById('date-debut');
    const dateFin = document.getElementById('date-fin');
    const motifTextarea = document.getElementById('motif');
    const motifCount = document.getElementById('motif-count');
    const dureeSpan = document.getElementById('duree-absence');
    const absenceInfo = document.getElementById('absence-info');
    const weekendWarning = document.getElementById('weekend-warning');

    // Compteur de caractères pour le motif
    if (motifTextarea && motifCount) {
        motifTextarea.addEventListener('input', function() {
            const count = this.value.length;
            motifCount.textContent = count;
            
            if (count > 450) {
                motifCount.classList.add('text-warning');
            } else if (count > 480) {
                motifCount.classList.remove('text-warning');
                motifCount.classList.add('text-danger');
            } else {
                motifCount.classList.remove('text-warning', 'text-danger');
            }
        });
    }

    // Calcul de la durée et validation des dates
    function calculateDuration() {
        if (dateDebut.value && dateFin.value) {
            const debut = new Date(dateDebut.value);
            const fin = new Date(dateFin.value);
            
            if (fin >= debut) {
                const diffTime = Math.abs(fin - debut);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
                
                dureeSpan.textContent = diffDays;
                absenceInfo.style.display = 'block';
                
                // Vérifier si la période inclut des week-ends
                let hasWeekend = false;
                for (let d = new Date(debut); d <= fin; d.setDate(d.getDate() + 1)) {
                    if (d.getDay() === 0 || d.getDay() === 6) {
                        hasWeekend = true;
                        break;
                    }
                }
                
                weekendWarning.style.display = hasWeekend ? 'block' : 'none';
                
                // Validation
                dateFin.setCustomValidity('');
            } else {
                dateFin.setCustomValidity('La date de fin doit être postérieure à la date de début');
                absenceInfo.style.display = 'none';
            }
        } else {
            absenceInfo.style.display = 'none';
        }
    }

    if (dateDebut && dateFin) {
        dateDebut.addEventListener('change', calculateDuration);
        dateFin.addEventListener('change', calculateDuration);
    }

    // Initialiser les tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
