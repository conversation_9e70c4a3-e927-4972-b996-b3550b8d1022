<!-- Modal d'approbation -->
<div class="modal fade" id="approveModal" tabindex="-1" aria-labelledby="approveModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="approveModalLabel">
                    <i class="bi bi-check-circle me-2"></i>
                    Approuver l'absence
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="approve-absence-details">
                    <!-- Les détails de l'absence seront chargés ici -->
                </div>
                
                <form id="approve-form">
                    <input type="hidden" id="approve-absence-id">
                    
                    <div class="mb-3">
                        <label for="approve-comment" class="form-label">Commentaire (optionnel)</label>
                        <textarea class="form-control" id="approve-comment" name="commentaire" rows="3" 
                                  placeholder="Ajoutez un commentaire sur cette approbation..."></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="approve-by" class="form-label">Approuvé par</label>
                        <input type="text" class="form-control" id="approve-by" name="approuve_par" 
                               placeholder="Votre nom" value="{{ user.get_full_name|default:user.username }}">
                    </div>
                </form>
                
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    Cette action approuvera définitivement la demande d'absence.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-1"></i>
                    Annuler
                </button>
                <button type="button" class="btn btn-success" id="btn-confirm-approve">
                    <span class="spinner-border spinner-border-sm me-2" id="approve-spinner" style="display: none;"></span>
                    <i class="bi bi-check-circle me-1" id="approve-icon"></i>
                    Approuver
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de rejet -->
<div class="modal fade" id="rejectModal" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="rejectModalLabel">
                    <i class="bi bi-x-circle me-2"></i>
                    Rejeter l'absence
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="reject-absence-details">
                    <!-- Les détails de l'absence seront chargés ici -->
                </div>
                
                <form id="reject-form">
                    <input type="hidden" id="reject-absence-id">
                    
                    <div class="mb-3">
                        <label for="reject-reason" class="form-label">
                            Motif du rejet <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control" id="reject-reason" name="motif" rows="4" 
                                  placeholder="Expliquez pourquoi cette demande est rejetée..." required></textarea>
                        <div class="invalid-feedback">
                            Le motif du rejet est obligatoire.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="reject-by" class="form-label">Rejeté par</label>
                        <input type="text" class="form-control" id="reject-by" name="rejete_par" 
                               placeholder="Votre nom" value="{{ user.get_full_name|default:user.username }}">
                    </div>
                </form>
                
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Cette action rejettera définitivement la demande d'absence. L'employé sera notifié du rejet.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-1"></i>
                    Annuler
                </button>
                <button type="button" class="btn btn-danger" id="btn-confirm-reject">
                    <span class="spinner-border spinner-border-sm me-2" id="reject-spinner" style="display: none;"></span>
                    <i class="bi bi-x-circle me-1" id="reject-icon"></i>
                    Rejeter
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="bi bi-trash me-2"></i>
                    Supprimer l'absence
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="delete-absence-details">
                    <!-- Les détails de l'absence seront chargés ici -->
                </div>
                
                <input type="hidden" id="delete-absence-id">
                
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>Attention !</strong> Cette action est irréversible. L'absence sera définitivement supprimée.
                </div>
                
                <p>Êtes-vous sûr de vouloir supprimer cette absence ?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-1"></i>
                    Annuler
                </button>
                <button type="button" class="btn btn-danger" id="btn-confirm-delete">
                    <span class="spinner-border spinner-border-sm me-2" id="delete-spinner" style="display: none;"></span>
                    <i class="bi bi-trash me-1" id="delete-icon"></i>
                    Supprimer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Template pour afficher les détails d'une absence -->
<script type="text/template" id="absence-details-template">
    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <strong>Employé:</strong> <span class="employe-nom"></span>
                </div>
                <div class="col-md-6">
                    <strong>Type:</strong> <span class="type-absence"></span>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-6">
                    <strong>Date début:</strong> <span class="date-debut"></span>
                </div>
                <div class="col-md-6">
                    <strong>Date fin:</strong> <span class="date-fin"></span>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-6">
                    <strong>Durée:</strong> <span class="duree"></span> jour(s)
                </div>
                <div class="col-md-6">
                    <strong>Impact paie:</strong> <span class="impact-paie"></span>
                </div>
            </div>
            <div class="row mt-2" style="display: none;" class="motif-row">
                <div class="col-12">
                    <strong>Motif:</strong> <span class="motif"></span>
                </div>
            </div>
        </div>
    </div>
</script>
