{% extends 'base/base.html' %}
{% load static %}

{% block title %}Gestion des Absences{% endblock title %}

{% block content %}
<div class="main-wrapper">
    {% include 'includes/header.html' %}
    {% include 'includes/sidebar.html' %}
    
    <div class="page-wrapper">
        <div class="container-fluid mt-4">
            <!-- En-tête avec titre et bouton d'ajout -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <h1 class="h3 mb-0">
                        <i class="bi bi-calendar-x me-2"></i>
                        Gestion des Absences
                    </h1>
                    <p class="text-muted">Gérez les demandes d'absence de vos employés</p>
                </div>
                <div class="col-md-4 text-end">
                    <button type="button" class="btn btn-primary" id="btn-nouvelle-absence">
                        <i class="bi bi-plus-circle me-2"></i>
                        Nouvelle Absence
                    </button>
                </div>
            </div>

            <!-- Compteurs de résumé -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0" id="total-absences">0</h4>
                                    <p class="mb-0">Total Absences</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-calendar-check fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0" id="en-attente">0</h4>
                                    <p class="mb-0">En Attente</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-clock fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0" id="approuvees">0</h4>
                                    <p class="mb-0">Approuvées</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-check-circle fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0" id="rejetees">0</h4>
                                    <p class="mb-0">Rejetées</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-x-circle fs-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filtres -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-funnel me-2"></i>
                        Filtres
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="filter-employe" class="form-label">Employé</label>
                            <select class="form-select" id="filter-employe">
                                <option value="">Tous les employés</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="filter-date-debut" class="form-label">Date début</label>
                            <input type="date" class="form-control" id="filter-date-debut">
                        </div>
                        <div class="col-md-2">
                            <label for="filter-date-fin" class="form-label">Date fin</label>
                            <input type="date" class="form-control" id="filter-date-fin">
                        </div>
                        <div class="col-md-2">
                            <label for="filter-type" class="form-label">Type</label>
                            <select class="form-select" id="filter-type">
                                <option value="">Tous les types</option>
                                {% for code, label in types_absence.items %}
                                <option value="{{ code }}">{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="filter-statut" class="form-label">Statut</label>
                            <select class="form-select" id="filter-statut">
                                <option value="">Tous les statuts</option>
                                {% for code, label in statuts_absence.items %}
                                <option value="{{ code }}">{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-1 d-flex align-items-end">
                            <button type="button" class="btn btn-outline-secondary" id="btn-reset-filters">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tableau des absences -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Liste des Absences</h5>
                    <div class="d-flex align-items-center">
                        <input type="text" class="form-control form-control-sm me-2" 
                               id="search-input" placeholder="Rechercher..." style="width: 200px;">
                        <button type="button" class="btn btn-outline-primary btn-sm" id="btn-refresh">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Loading spinner -->
                    <div id="loading-spinner" class="text-center py-4" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                        <p class="mt-2">Chargement des absences...</p>
                    </div>

                    <!-- Tableau -->
                    <div class="table-responsive">
                        <table class="table table-hover" id="absences-table">
                            <thead class="table-light">
                                <tr>
                                    <th class="sortable" data-sort="employe_nom">
                                        Employé <i class="bi bi-arrow-down-up ms-1"></i>
                                    </th>
                                    <th class="sortable" data-sort="date_debut">
                                        Date début <i class="bi bi-arrow-down-up ms-1"></i>
                                    </th>
                                    <th class="sortable" data-sort="date_fin">
                                        Date fin <i class="bi bi-arrow-down-up ms-1"></i>
                                    </th>
                                    <th class="sortable" data-sort="type_absence">
                                        Type <i class="bi bi-arrow-down-up ms-1"></i>
                                    </th>
                                    <th class="sortable" data-sort="statut">
                                        Statut <i class="bi bi-arrow-down-up ms-1"></i>
                                    </th>
                                    <th>Motif</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="absences-tbody">
                                <!-- Les données seront chargées via AJAX -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Message si aucune donnée -->
                    <div id="no-data-message" class="text-center py-4" style="display: none;">
                        <i class="bi bi-inbox fs-1 text-muted"></i>
                        <p class="text-muted mt-2">Aucune absence trouvée</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals -->
{% include 'absence/_absence_form_modal.html' %}
{% include 'absence/_absence_actions_modal.html' %}

<!-- Toast container -->
<div class="toast-container position-fixed bottom-0 end-0 p-3" id="toast-container">
</div>
<script>
    // Configuration globale
    window.ABSENCE_CONFIG = {
        urls: {
            list: "{% url 'absence:api_absence_list' %}",
            create: "{% url 'absence:api_absence_create' %}",
            detail: "{% url 'absence:api_absence_detail' absence_id='__ID__' %}",
            update: "{% url 'absence:api_absence_update' absence_id='__ID__' %}",
            action: "{% url 'absence:api_absence_action' absence_id='__ID__' action='__ACTION__' %}",
            delete: "{% url 'absence:api_absence_delete' absence_id='__ID__' %}",
            employes: "{% url 'absence:api_employe_list' %}"
        },
        types_absence: {{ types_absence_json|safe }},
        statuts_absence: {{ statuts_absence_json|safe }}
    };
</script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="{% static 'absence/js/absences.js' %}"></script>
{% endblock content %}
