---
applyTo: '**'
---

## 🎨 Directives Frontend – Bootstrap + CSS

---

### 1. **Structure & Organisation**

✅ **Fais ça**

* Organise le code avec un dossier `static/` séparé par app (`static/app_name/css`, `static/app_name/js`).
* Regroupe le CSS personnalisé dans un fichier dédié (`custom.css`).
* Respecte la hiérarchie HTML (titres `<h1>` → `<h6>`, sections `<section>` / `<article>`).
* Utilise les classes Bootstrap avant de créer du CSS personnalisé.
* Commente les sections importantes du HTML et CSS.

❌ **Ne fais pas ça**

* Ne mets pas tout le style en inline (`style="..."`).
* Ne surcharge pas Bootstrap sans raison valable.
* Ne mélange pas HTML, CSS et JS dans un même fichier inutilement.
* Ne laisse pas du code mort (classes inutilisées).
* Ne crée pas de fichiers css trop volumineux (sépare par fonctionnalité).

---

### 2. **Bootstrap – Bonnes Pratiques**

✅ **Fais ça**

* Utilise le **système de grille** (`.container`, `.row`, `.col-*`) correctement.
* Utilise les classes utilitaires (`.mt-3`, `.text-center`, etc.) pour des ajustements rapides.
* Respecte les breakpoints (`col-md-6`, `col-lg-4`) pour un rendu responsive.
* Utilise les composants Bootstrap (navbar, modals, cards) au lieu de tout recoder.

❌ **Ne fais pas ça**

* Ne mets pas trop de classes utilitaires dans un seul élément (lisibilité).
* Ne désactive pas la responsive design (`<meta name="viewport">`).
* Ne modifie pas directement `bootstrap.min.css` (fais des overrides dans `custom.css`).

---

### 3. **CSS – Bonnes Pratiques**

✅ **Fais ça**

* Utilise des noms de classes explicites (`.btn-login` au lieu de `.bl`).
* Grouper le CSS par section/component (header, footer, forms).
* Utilise `:root { --primary-color: ... }` pour définir des variables CSS réutilisables.
* Minifie le CSS en production (`django-compressor` ou `whitenoise`).

❌ **Ne fais pas ça**

* Ne dupliques pas les règles CSS (factorise-les).
* Ne fixes pas les tailles avec `px` uniquement (utilise `%` ou `rem` pour adaptabilité).
* Ne laisses pas des styles inutilisés.
* Ne surcharge pas Bootstrap avec des règles trop générales (`body { ... }`).

---

### 4. **Accessibilité & SEO**

✅ **Fais ça**

* Utilise toujours `alt=""` pour les images.
* Respecte le contraste texte/fond.
* Utilise des titres `<h1>` uniques par page.
* Garde une structure HTML sémantique (pas de `<div>` pour tout).

❌ **Ne fais pas ça**

* Ne cache pas du texte important uniquement avec CSS.
* Ne mets pas du contenu texte uniquement dans des images sans alternative.

---

## 📋 Check-list Automatisable – Frontend

---

### **1. Structure & Organisation**

* [ ] Les fichiers CSS/JS personnalisés sont dans `static/`.
* [ ] Aucun style inline (`style="..."`).
* [ ] Le HTML est structuré avec des balises sémantiques (`header`, `main`, `footer`).
* [ ] Les sections HTML/CSS sont commentées.
* [ ] Aucun code CSS inutilisé.

---

### **2. Bootstrap**

* [ ] Le système de grille est utilisé correctement (`container` → `row` → `col-*`).
* [ ] Les breakpoints Bootstrap sont respectés (`col-md-*`, `col-lg-*`).
* [ ] Les composants Bootstrap sont utilisés au lieu de tout recoder.
* [ ] Aucun fichier Bootstrap original modifié.

---

### **3. CSS**

* [ ] Les noms de classes sont explicites.
* [ ] Le CSS est regroupé par section/component.
* [ ] Les variables CSS sont utilisées pour les couleurs et tailles récurrentes.
* [ ] Le CSS est minifié en production.
* [ ] Aucun doublon de règles CSS.

---

### **4. Accessibilité & SEO**

* [ ] Les images ont un attribut `alt`.
* [ ] Le contraste texte/fond respecte les normes WCAG.
* [ ] Chaque page a un seul `<h1>`.
* [ ] HTML sémantique respecté.
