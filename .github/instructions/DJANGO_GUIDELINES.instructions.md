---
applyTo: '**'
---

## Directives – Projet Django

---

### **1. Structure du Projet**

* [ ] Le projet a été créé avec `django-admin startproject` et les apps avec `startapp`.
* [ ] Chaque application correspond à une fonctionnalité métier distincte.
* [ ] Les paramètres sont séparés (`settings/base.py`, `settings/dev.py`, `settings/prod.py`).
* [ ] Les fichiers statiques et templates sont rangés par application.
* [ ] Aucun code sensible n’est directement écrit dans `settings.py`.
* [ ] Aucun projet monolithique “tout-en-un” n’a été créé.

---

### **2. Base de Données & Modèles**

* [ ] Tous les champs importants ont `verbose_name` et `help_text`.
* [ ] Les contraintes (`unique`, `choices`, `validators`) sont utilisées si nécessaires.
* [ ] Les champs filtrés fréquemment ont `db_index=True`.
* [ ] Chaque relation (`ForeignKey`, `ManyToMany`) est documentée.
* [ ] Les migrations sont synchronisées avec les modèles.
* [ ] Aucun fichier de migration n’est modifié manuellement sauf nécessité absolue.
* [ ] Aucune donnée sensible n’est stockée en clair.
* [ ] Les requêtes lourdes utilisent `select_related` / `prefetch_related`.

---

### **3. Sécurité**

* [ ] `DEBUG = False` en production.
* [ ] `ALLOWED_HOSTS` contient uniquement les domaines autorisés.
* [ ] `SECRET_KEY` et autres secrets sont dans les variables d’environnement.
* [ ] Les protections CSRF, XSS et clicjacking sont activées.
* [ ] `HTTPS` est activé avec `SECURE_SSL_REDIRECT = True` en prod.
* [ ] Les permissions et authentifications sont vérifiées dans chaque vue/API.
* [ ] Aucun mot de passe en dur dans le code.

---

### **4. Vues, Logique Métier & API**

* [ ] Les vues utilisent CBV ou ViewSets si nécessaire.
* [ ] La logique métier est séparée dans des services/helpers.
* [ ] Les réponses d’API sont claires et structurées.
* [ ] Les erreurs sont gérées proprement (`try/except` ciblés).
* [ ] Aucune donnée sensible n’est exposée dans les réponses.
* [ ] Pas de logique métier lourde dans les templates.
* [ ] Les vues sont testées avec des tests unitaires.
* [ ] Pas de vues trop volumineuses : modularise.

---

### **5. Templates & Frontend**

* [ ] Les templates sont organisés par application.
* [ ] L’héritage `{% extends %}` est utilisé pour éviter la duplication.
* [ ] Les fichiers CSS/JS sont chargés via `{% static %}`.
* [ ] Aucune donnée sensible n’est injectée dans le HTML.
* [ ] Aucun code Python lourd dans les templates.
* [ ] Pas de templates trop volumineux : modularise.
* [ ] Assure toi que tu importes toujours l'asidebar et le header sur les nouvelles pages.
* [ ] Assure toi d'ajouter plus d'info sur les erreurs pour faciliter le debogage.
* [ ] Assure toi d'utiliser AJAX pour une meilleur performance.

---

### **6. Tests & Qualité**

* [ ] Des tests unitaires et d’intégration existent pour les fonctionnalités principales.
* [ ] Les cas limites et erreurs sont testés.
* [ ] Le taux de couverture est mesuré avec `coverage`.
* [ ] Les tests passent avant tout déploiement.
* [ ] Aucun test incomplet ou commenté.

---

### **7. Performance**

* [ ] Les requêtes ORM sont optimisées avec `select_related` et `prefetch_related`.
* [ ] Un cache est utilisé pour les données coûteuses à calculer.
* [ ] Les fichiers statiques sont compressés/minifiés.
* [ ] Les vues avec beaucoup de résultats sont paginées.

---

### **8. Déploiement & Maintenance**

* [ ] `collectstatic` est exécuté avant chaque déploiement.
* [ ] Sauvegarde DB effectuée avant migration en production.
* [ ] Le serveur tourne avec `gunicorn`/`uvicorn` derrière `nginx`.
* [ ] Documentation d’installation/mise à jour à jour.
* [ ] Aucun fichier temporaire ou debug sur le serveur.
