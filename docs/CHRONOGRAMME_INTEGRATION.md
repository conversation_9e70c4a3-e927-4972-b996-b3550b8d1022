# Chronogramme Intégration Comptabilité - 5 jours

**Période :** <PERSON><PERSON> → Samedi  
**Samedi :** Demi-journ<PERSON> (4h)  
**Objectif :** Intégrer l'API comptabilité dans l'app `accounting` existante de Lotus

## Planning Global

| Jour | Du<PERSON><PERSON> | Objectif Principal | Livrables |
|------|-------|-------------------|-----------|
| **MARDI** | 8h | Configuration + API Client | Service API + Navigation intégrée |
| **MERCREDI** | 8h | Dashboard + Sociétés | Dashboard comptable + CRUD sociétés |
| **JEUDI** | 8h | Plan Comptable + Écritures | Interface hiérarchique + Saisie |
| **VENDREDI** | 8h | États + Rapports | Balance, bilan + Export |
| **SAMEDI** | 4h | Tests + Polish | Module comptabilité intégré |

## Détail Quotidien

### MARDI - Jour 1 (8h)
**Objectif :** Fondations et intégration

**Matin (4h)**
- [ ] Créer structure dans `apps/accounting/`
  - `services/` (api_client.py, auth_service.py)
  - `templates/accounting/`
  - `static/accounting/`
- [ ] Configuration API client Python
- [ ] Créer `urls.py` dans accounting
- [ ] Ajouter route dans `config/urls.py`

**Après-midi (4h)**
- [ ] **Intégrer dans sidebar existante**
  - Modifier `templates/includes/sidebar.html`
  - Ajouter section "Comptabilité" dans l'accordéon
  - Icons et navigation cohérente
- [ ] Créer templates de base (`base.html`)
- [ ] Tests connexion API
- [ ] Gestion erreurs API

**Livrable :** Navigation comptabilité intégrée + Service API

---

### MERCREDI - Jour 2 (8h)
**Objectif :** Dashboard et gestion sociétés

**Matin (4h)**
- [ ] **Dashboard comptable**
  - Vue `dashboard()` avec KPIs API
  - Template suivant le pattern Lotus
  - Cards financières (CA, résultat, etc.)
  - Graphiques Chart.js intégrés

**Après-midi (4h)**
- [ ] **CRUD Sociétés**
  - Vues : list, create, detail, update
  - Formulaires Django (`SocieteForm`)
  - Templates liste/formulaire
  - Style cohérent avec autres modules

**Livrable :** Dashboard + Gestion sociétés complète

---

### JEUDI - Jour 3 (8h)
**Objectif :** Plan comptable et écritures

**Matin (4h)**
- [ ] **Plan comptable hiérarchique**
  - Vue avec arbre des comptes
  - JavaScript expand/collapse
  - Filtres par classe (1-8)
  - Recherche de comptes

**Après-midi (4h)**
- [ ] **Saisie écritures**
  - Formulaire multi-lignes dynamique
  - Auto-complétion comptes/tiers
  - Validation équilibrage temps réel
  - Actions : ajouter/supprimer lignes

**Livrable :** Plan comptable + Saisie écritures

---

### VENDREDI - Jour 4 (8h)
**Objectif :** États comptables et rapports

**Matin (4h)**
- [ ] **États comptables**
  - Balance générale avec paramètres
  - Grand livre par compte
  - Interface bilan comptable
  - Compte de résultat

**Après-midi (4h)**
- [ ] **Export et rapports**
  - Export Excel/PDF
  - Paramètres d'export
  - Liste écritures avec filtres
  - Actions groupées

**Livrable :** États comptables + Export

---

### SAMEDI - Jour 5 (4h)
**Objectif :** Finalisation

**Matin uniquement (4h)**
- [ ] Tests d'intégration complets
- [ ] Optimisations UX/UI
- [ ] Corrections bugs
- [ ] Documentation utilisateur

**Livrable :** Module comptabilité intégré et testé

## Architecture d'Intégration

### Structure finale `apps/accounting/` :
```
apps/accounting/
├── __init__.py
├── admin.py
├── apps.py
├── models.py
├── views.py              # Vues principales
├── urls.py               # URLs (nouveau)
├── forms.py              # Formulaires (nouveau)
├── services/             # Services API (nouveau)
│   ├── __init__.py
│   ├── api_client.py
│   ├── auth_service.py
│   └── data_service.py
├── templates/accounting/ # Templates (nouveau)
│   ├── base.html
│   ├── dashboard.html
│   ├── societes/
│   ├── comptes/
│   ├── ecritures/
│   └── rapports/
├── static/accounting/    # Assets (nouveau)
│   ├── css/
│   ├── js/
│   └── img/
└── tests.py
```

### Intégration dans Lotus :

#### Navigation (sidebar.html) :
```html
<!-- Section Comptabilité dans l'accordéon -->
<div class="accordion-item border-0">
    <h2 class="accordion-header" id="accountingHeading">
        <button class="accordion-button collapsed" type="button" 
                data-bs-toggle="collapse" data-bs-target="#accountingCollapse">
            <i class="bi bi-calculator fs-16 me-2"></i>
            <span>Comptabilité</span>
        </button>
    </h2>
    <div id="accountingCollapse" class="accordion-collapse collapse">
        <div class="accordion-body p-0">
            <ul class="list-unstyled">
                <li><a href="{% url 'accounting:dashboard' %}">Dashboard</a></li>
                <li><a href="{% url 'accounting:societe_list' %}">Sociétés</a></li>
                <li><a href="{% url 'accounting:compte_list' %}">Plan comptable</a></li>
                <li><a href="{% url 'accounting:ecriture_list' %}">Écritures</a></li>
                <li><a href="{% url 'accounting:rapport_list' %}">Rapports</a></li>
            </ul>
        </div>
    </div>
</div>
```

#### URLs (config/urls.py) :
```python
urlpatterns = [
    # ... URLs existantes ...
    path("comptabilite/", include("apps.accounting.urls")),
]
```

#### Service API :
```python
# apps/accounting/services/api_client.py
class ComptabiliteAPIClient:
    def __init__(self):
        self.base_url = settings.COMPTABILITE_API_URL
        self.api_key = settings.COMPTABILITE_API_KEY
        
    def get_societes(self):
        # Appel API /societes
        
    def create_ecriture(self, data):
        # Appel API /ecritures
```

## Fonctionnalités Intégrées

### Fin de semaine - Modules opérationnels :
- ✅ **Dashboard** : KPIs financiers + graphiques
- ✅ **Sociétés** : CRUD complet + statistiques
- ✅ **Plan comptable** : Hiérarchie + recherche + classes
- ✅ **Écritures** : Saisie multi-lignes + validation
- ✅ **États** : Balance, bilan, grand livre
- ✅ **Export** : Excel/PDF des rapports
- ✅ **Navigation** : Intégrée dans sidebar Lotus
- ✅ **Style** : Cohérent avec le design existant

### APIs consommées :
- `/api/v1/societes/*` - Gestion sociétés
- `/api/v1/comptes/*` - Plan comptable
- `/api/v1/ecritures/*` - Écritures comptables
- `/api/v1/calculs/*` - Balance, grand livre
- `/api/v1/etats/*` - Bilan, compte résultat
- `/api/v1/dashboard/*` - KPIs et analyses

## Configuration Requise

### Settings (base.py) :
```python
# API Comptabilité SYSCOHADA
COMPTABILITE_API_URL = os.getenv('COMPTABILITE_API_URL', 'http://localhost:3000/api/v1')
COMPTABILITE_API_KEY = os.getenv('COMPTABILITE_API_KEY', 'your_api_key')
COMPTABILITE_API_TIMEOUT = 30
```

### Variables d'environnement :
```bash
# .env
COMPTABILITE_API_URL=http://localhost:3000/api/v1
COMPTABILITE_API_KEY=sk_your_api_key_here
```

## Points d'Attention

### Cohérence avec Lotus :
1. **Style** : Réutiliser classes CSS existantes
2. **Navigation** : Intégrer dans sidebar accordéon
3. **Permissions** : Utiliser système existant
4. **Messages** : Utiliser Django messages framework
5. **Patterns** : Suivre structure des autres apps

### Priorités :
- **Jour 1-2** : Fondations + Dashboard (critique)
- **Jour 3** : Saisie écritures (fonctionnalité clé)
- **Jour 4** : États comptables (important)
- **Jour 5** : Polish + tests (qualité)

## Métriques de Succès

- **Intégration** : Navigation fluide dans Lotus
- **Fonctionnalités** : 15+ écrans opérationnels
- **Performance** : <2s chargement pages
- **UX** : Interface cohérente avec l'existant
- **API** : Gestion robuste des erreurs

---

**Objectif :** Module comptabilité parfaitement intégré dans Lotus ! 🎯
