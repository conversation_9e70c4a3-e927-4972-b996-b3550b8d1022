{% load static %}
{% load permission_tags %}
<style>
    .vertical-text {
        writing-mode: vertical-rl; /* Rend le texte vertical */
        transform: rotate(180deg); /* Optionnel : ajuste l'orientation si nécessaire */
        text-align: center; /* Centre le texte */
    }
    .sidebar-twocol.sidebar .nav-link.active, .sidebar-twocol.sidebar .nav-link:hover {
        background: #FFF6EE;
        color: #4D555E;
        width: max-content;
        height: max-content;
    }
    .sidebar-twocol.sidebar .nav-link.active, .sidebar-twocol.sidebar .nav-link {
        width: max-content;
        height: max;
    }

    /* Styles pour l'accordéon de la sidebar */
    #sidebarAccordion .accordion-item {
        background: transparent;
        border: none !important;
        margin-bottom: 15px;
    }

    #sidebarAccordion .accordion-button {
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
        padding: 8px 0 !important;
        font-weight: 600;
        font-size: 12px;
        color: #67748E !important;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        outline: none !important;
    }

    #sidebarAccordion .accordion-button:not(.collapsed) {
        background: transparent !important;
        color: #FE9F43 !important;
    }

    #sidebarAccordion .accordion-button::after {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23FE9F43'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e") !important;
        width: 12px;
        height: 12px;
        margin-left: auto;
        transition: transform 0.2s ease-in-out;
    }

    #sidebarAccordion .accordion-button:not(.collapsed)::after {
        transform: rotate(180deg);
    }

    #sidebarAccordion .accordion-body {
        padding: 0 !important;
        border-top: 1px solid #E5E7EB;
        margin-top: 8px;
        padding-top: 8px !important;
    }

    #sidebarAccordion .accordion-body ul li {
        margin-bottom: 2px;
    }

    #sidebarAccordion .accordion-body ul li a {
        padding: 8px 12px !important;
        color: #67748E;
        border-radius: 6px;
        transition: all 0.3s ease;
        font-size: 14px;
        font-weight: 500;
    }

    #sidebarAccordion .accordion-body ul li a:hover {
        background: #FFF6EE;
        color: #FE9F43;
        text-decoration: none;
    }

    #sidebarAccordion .accordion-body ul li.active a {
        background: #FFF6EE;
        color: #FE9F43;
        font-weight: 600;
    }

    #sidebarAccordion .accordion-body ul li.submenu > a {
        font-weight: 600;
    }

    #sidebarAccordion .accordion-body ul li.submenu ul {
        margin-top: 5px;
        padding-left: 0;
    }

    #sidebarAccordion .accordion-body ul li.submenu ul li a {
        padding-left: 35px !important;
        font-size: 13px;
        font-weight: 400;
    }

    /* Compatibilité avec le mode mini-sidebar */
    .mini-sidebar #sidebarAccordion .accordion-button span {
        display: none;
    }

    .mini-sidebar #sidebarAccordion .accordion-button::after {
        display: none;
    }

    .mini-sidebar.expand-menu #sidebarAccordion .accordion-button span {
        display: inline-block;
    }

    .mini-sidebar.expand-menu #sidebarAccordion .accordion-button::after {
        display: block;
    }

    /* Animation fluide pour l'accordéon */
    #sidebarAccordion .accordion-collapse {
        transition: height 0.35s ease;
    }

    /* Styles pour les flèches des sous-menus */
    .menu-arrow {
        width: 0;
        height: 0;
        border-left: 4px solid #67748E;
        border-top: 4px solid transparent;
        border-bottom: 4px solid transparent;
        transition: transform 0.2s ease;
        margin-left: auto;
    }

    .submenu.active .menu-arrow {
        transform: rotate(90deg);
    }
</style>
<!-- Sidebar -->
<div class="sidebar" id="sidebar">
    <!-- Logo -->
    <div class="sidebar-logo">
        <a href="/" class="logo logo-normal">
            <img src="{% static 'assets/img/logo.svg' %}" alt="Img" height="40" width="120">
        </a>
        <a href="/" class="logo logo-white">
            <img src="{% static 'assets/img/logo-white.svg' %}" alt="Img" height="40" width="120">
        </a>
        <a href="/" class="logo-small">
            <img src="{% static 'assets/img/logo-small.png' %}" alt="Img" height="32" width="32">
        </a>
        <a id="toggle_btn" href="#">
            <i data-feather="chevrons-left" class="feather-16"></i>
        </a>
    </div>
    <!-- /Logo -->
    <div class="modern-profile p-3 pb-0">
        <div class="text-center rounded bg-light p-3 mb-4 user-profile">
            <div class="avatar avatar-lg online mb-3">
                <i class="bi bi-person-circle text-primary" style="font-size: 64px;"></i>
            </div>
            <h6 class="fs-14 fw-bold mb-1">{{ user.get_full_name|default:"Adrian Herman" }}</h6>
            <p class="fs-12 mb-0">{% user_role_name user %}</p>
        </div>
        <div class="sidebar-nav mb-3">
            <ul class="nav nav-tabs nav-tabs-solid nav-tabs-rounded nav-justified bg-transparent"
                role="tablist">
                <li class="nav-item">
                    <a class="nav-link active border-0" href="expense-report.html#">Menu</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link border-0" href="chat.html">Chats</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link border-0" href="email.html">Inbox</a>
                </li>
            </ul>
        </div>
    </div>
    <div class="sidebar-header p-3 pb-0 pt-2">
        <div class="text-center rounded bg-light p-2 mb-4 sidebar-profile d-flex align-items-center">
            <div class="avatar avatar-md onlin">
                <i class="bi bi-person-circle text-primary" style="font-size: 48px;"></i>
            </div>
            <div class="text-start sidebar-profile-info ms-2">
                <h6 class="fs-14 fw-bold mb-1">{{ user.get_full_name|default:"Adrian Herman" }}</h6>
                <p class="fs-12">{% user_role_name user %}</p>
            </div>
        </div>
        <div class="d-flex align-items-center justify-content-between menu-item mb-3">
            <div>
                <a href="index.html" class="btn btn-sm btn-icon bg-light">
                    <i class="bi bi-grid-3x3-gap"></i>
                </a>
            </div>
            <div>
                <a href="chat.html" class="btn btn-sm btn-icon bg-light">
                    <i class="bi bi-chat-dots"></i>
                </a>
            </div>
            <div>
                <a href="email.html"
                   class="btn btn-sm btn-icon bg-light position-relative">
                    <i class="bi bi-envelope"></i>
                </a>
            </div>
            <div class="notification-item">
                <a href="activities.html"
                   class="btn btn-sm btn-icon bg-light position-relative">
                    <i class="bi bi-bell"></i>
                    <span class="notification-status-dot"></span>
                </a>
            </div>
            <div class="me-0">
                <a href="general-settings.html" class="btn btn-sm btn-icon bg-light">
                    <i class="bi bi-gear"></i>
                </a>
            </div>
        </div>
    </div>
    <div class="sidebar-inner slimscroll">
        <div id="sidebar-menu" class="sidebar-menu">
            <!-- Accordéon Bootstrap pour la navigation -->
            <div class="accordion accordion-flush" id="sidebarAccordion">
                {% if user.role == 'SELLER' %}
                <!-- Menu spécial vendeur : uniquement la vente -->
                <div class="accordion-item border-0">
                    {% comment %} <h2 class="accordion-header" id="salesHeading">
                        <button class="accordion-button bg-transparent border-0 p-0 submenu-hdr" type="button"
                                data-bs-toggle="collapse" data-bs-target="#salesCollapse"
                                aria-expanded="{% if '/sales/sale/' in request.path %}true{% else %}false{% endif %}" aria-controls="salesCollapse">
                            <i class="bi bi-cash-coin fs-16 me-2"></i>
                            <span>Vente</span>
                        </button>
                    </h2> {% endcomment %}
                    {% comment %} <div id="salesCollapse" class="accordion-collapse collapse {% if '/sales/sale/' in request.path %}show{% endif %}"
                         aria-labelledby="salesHeading" data-bs-parent="#sidebarAccordion">
                        <div class="accordion-body p-0">
                            <ul class="list-unstyled">
                                <li class="{% if '/sales/sale/' in request.path %}active{% endif %}">
                                    <a href="/sales/sale/" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-cart me-2"></i>
                                        <span>Nouvelle vente</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div> {% endcomment %}
                </div>
                {% endif %}

                <!-- Les autres menus pour les rôles non-vendeurs -->
                <!-- Section Vue d'ensemble -->
                {% if user|can_access:"overview" and user.role != 'SELLER' %}
                <div class="accordion-item border-0">
                    <h2 class="accordion-header" id="overviewHeading">
                        <button class="accordion-button collapsed bg-transparent border-0 p-0 submenu-hdr" type="button"
                                data-bs-toggle="collapse" data-bs-target="#overviewCollapse"
                                aria-expanded="{% if request.path == '/' %}true{% else %}false{% endif %}" aria-controls="overviewCollapse">
                            <i class="bi bi-grid-3x3-gap fs-16 me-2"></i>
                            <span>Vue d'ensemble</span>
                        </button>
                    </h2>
                    <div id="overviewCollapse" class="accordion-collapse collapse {% if request.path == '/' %}show{% endif %}"
                         aria-labelledby="overviewHeading" data-bs-parent="#sidebarAccordion">
                        <div class="accordion-body p-0">
                            <ul class="list-unstyled">
                                <li class="{% if request.path == '/' %}active{% endif %}">
                                    <a href="/" class="d-flex align-items-center text-decoration-none p-2">
                                        <i data-feather="box" class="me-2"></i>
                                        <span>Panneau de controle</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Section SIRH / Paie -->
                {% comment %} {% if user|has_perm:"sirh" %} {% endcomment %}
                <div class="accordion-item border-0">
                    <h2 class="accordion-header" id="sirhHeading">
                        <button class="accordion-button collapsed bg-transparent border-0 p-0 submenu-hdr" type="button"
                                data-bs-toggle="collapse" data-bs-target="#sirhCollapse"
                                aria-expanded="{% if '/sirh/employes/' in request.path %}true{% else %}false{% endif %}"
                                aria-controls="sirhCollapse">
                            <i class="bi bi-people fs-16 me-2"></i>
                            <span>Paie & RH</span>
                        </button>
                    </h2>
                    <div id="sirhCollapse" class="accordion-collapse collapse {% if '/sirh/employes/' in request.path %}show{% endif %}"
                         aria-labelledby="sirhHeading" data-bs-parent="#sidebarAccordion">
                        <div class="accordion-body p-0">
                            <ul class="list-unstyled">
                                <li class="{% if '/sirh/employes/' in request.path %}active{% endif %}">
                                    <a href="{% url 'sirh:employe_list' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-person-badge fs-16 me-2"></i>
                                        <span>Employés</span>
                                    </a>
                                </li>
                                <li class="{% if '/sirh/bulletins/' in request.path %}active{% endif %}">
                                    <a href="{% url 'sirh:bulletin_list' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-file-earmark-text fs-16 me-2"></i>
                                        <span>Bulletins de paie</span>
                                    </a>
                                </li>
                                <li class="{% if '/prets/' in request.path or '/avances/' in request.path %}active{% endif %}">
                                    <a href="{% url 'prets:loan_list' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-cash-coin fs-16 me-2"></i>
                                        <span>Prêts / Avances</span>
                                    </a>
                                </li>
                                <li class="{% if '/pointage/' in request.path %}active{% endif %}">
                                    <a href="{% url 'pointage:pointage_list' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-clock-history fs-16 me-2"></i>
                                        <span>Pointage</span>
                                    </a>
                                </li>
                                <li class="{% if '/absences/' in request.path %}active{% endif %}">
                                    <a href="{% url 'absence:absence_list' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-calendar-x fs-16 me-2"></i>
                                        <span>Absences</span>
                                    </a>
                                </li>
                                    <li class="{% if '/hours/' in request.path %}active{% endif %}">
                                        <a href="/hours/" class="d-flex align-items-center text-decoration-none p-2">
                                            <i class="bi bi-hourglass-split fs-16 me-2"></i>
                                            <span>Heures supplémentaires</span>
                                        </a>
                                    </li>
                                <li class="{% if '/sirh/sante/' in request.path %}active{% endif %}">
                                    <a href="{% url 'sirh:sante_list' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-heart-pulse fs-16 me-2"></i>
                                        <span>Paramètres RH</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                {% comment %} {% endif %} {% endcomment %}

                <!-- Section Client & Fournisseur -->
                {% if user|can_access:"clients" %}
                <div class="accordion-item border-0">
                    <h2 class="accordion-header" id="clientsHeading">
                        <button class="accordion-button collapsed bg-transparent border-0 p-0 submenu-hdr" type="button"
                                data-bs-toggle="collapse" data-bs-target="#clientsCollapse"
                                aria-expanded="{% if '/client/' in request.path or '/grossiste/' in request.path or '/deposits/' in request.path or '/supplier/' in request.path or request.path == '/store-list/' %}true{% else %}false{% endif %}"
                                aria-controls="clientsCollapse">
                            <i class="bi bi-people fs-16 me-2"></i>
                            <span>Client & Fournisseur</span>
                        </button>
                    </h2>
                    <div id="clientsCollapse" class="accordion-collapse collapse {% if '/client/' in request.path or '/grossiste/' in request.path or '/deposits/' in request.path or '/supplier/' in request.path or request.path == '/store-list/' %}show{% endif %}"
                         aria-labelledby="clientsHeading" data-bs-parent="#sidebarAccordion">
                        <div class="accordion-body p-0">
                            <ul class="list-unstyled">
                                {% if user|has_perm:"clients" or user|has_perm:"grossistes" %}
                                <li class="submenu">
                                    <a href="#" class="d-flex align-items-center justify-content-between text-decoration-none p-2">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-people fs-16 me-2"></i>
                                            <span>Clients</span>
                                        </div>
                                        <span class="menu-arrow"></span>
                                    </a>
                                    <ul class="list-unstyled ms-3">
                                        {% if user|has_perm:"clients" %}
                                        <li class="{% if '/client/' in request.path %}active{% endif %}">
                                            <a href="{% url 'client_list' %}" class="d-flex align-items-center text-decoration-none p-2">
                                                <i class="bi bi-person fs-16 me-2"></i>
                                                <span>Clients</span>
                                            </a>
                                        </li>
                                        {% endif %}
                                        {% if user|has_perm:"grossistes" %}
                                        <li class="{% if '/grossiste/' in request.path %}active{% endif %}">
                                            <a href="{% url 'grossiste_list' %}" class="d-flex align-items-center text-decoration-none p-2">
                                                <i class="bi bi-person-badge fs-16 me-2"></i>
                                                <span>Clients Grossiste</span>
                                            </a>
                                        </li>
                                        {% endif %}
                                    </ul>
                                </li>
                                {% endif %}
                                {% if user|has_perm:"deposits" %}
                                <li class="submenu">
                                    <a href="#" class="d-flex align-items-center justify-content-between text-decoration-none p-2">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-piggy-bank fs-16 me-2"></i>
                                            <span>Dépôts Clients</span>
                                        </div>
                                        <span class="menu-arrow"></span>
                                    </a>
                                    <ul class="list-unstyled ms-3">
                                        <li class="{% if '/deposits/' in request.path and 'create' not in request.path and 'statistics' not in request.path %}active{% endif %}">
                                            <a href="{% url 'deposits:deposit_list' %}" class="d-flex align-items-center text-decoration-none p-2">
                                                <i class="bi bi-list-ul fs-16 me-2"></i>
                                                <span>Consultation des dépôts</span>
                                            </a>
                                        </li>
                                        <li class="{% if '/deposits/create/' in request.path %}active{% endif %}">
                                            <a href="{% url 'deposits:deposit_create' %}" class="d-flex align-items-center text-decoration-none p-2">
                                                <i class="bi bi-plus-circle fs-16 me-2"></i>
                                                <span>Nouveau dépôt</span>
                                            </a>
                                        </li>
                                        {% comment %} <li class="{% if '/deposits/statistics/' in request.path %}active{% endif %}">
                                            <a href="{% url 'deposits:deposit_statistics' %}" class="d-flex align-items-center text-decoration-none p-2">
                                                <i class="bi bi-graph-up fs-16 me-2"></i>
                                                <span>Statistiques</span>
                                            </a>
                                        </li> {% endcomment %}
                                    </ul>
                                </li>
                                {% endif %}
                                {% if user|has_perm:"suppliers" %}
                                <li class="{% if '/supplier/' in request.path %}active{% endif %}">
                                    <a href="{% url 'supplier_list' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-person-plus fs-16 me-2"></i>
                                        <span>Fournisseur</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% comment %} {% if user|has_perm:"shops" %} {% endcomment %}
                                <li class="{% if request.path == '/store-list/' %}active{% endif %}">
                                    <a href="{% url 'shop_list' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-shop fs-16 me-2"></i>
                                        <span>Boutiques</span>
                                    </a>
                                </li>
                                {% comment %} {% endif %} {% endcomment %}
                            </ul>
                        </div>
                    </div>
                </div>
                {% endif %}
                <!-- Section Gestion du stock -->
                {% if user|can_access:"stock" %}
                <div class="accordion-item border-0">
                    <h2 class="accordion-header" id="stockHeading">
                        <button class="accordion-button collapsed bg-transparent border-0 p-0 submenu-hdr" type="button"
                                data-bs-toggle="collapse" data-bs-target="#stockCollapse"
                                aria-expanded="{% if '/stock/' in request.path or request.path == '/fiche-de-stcok/' %}true{% else %}false{% endif %}"
                                aria-controls="stockCollapse">
                            <i class="bi bi-box fs-16 me-2"></i>
                            <span>Gestion du stock</span>
                        </button>
                    </h2>
                    <div id="stockCollapse" class="accordion-collapse collapse {% if '/stock/' in request.path or request.path == '/fiche-de-stcok/' %}show{% endif %}"
                         aria-labelledby="stockHeading" data-bs-parent="#sidebarAccordion">
                        <div class="accordion-body p-0">
                            <ul class="list-unstyled">
                                {% if user|has_perm:"products" %}
                                <li class="{% if request.path == '/stock/products/list/' %}active{% endif %}">
                                    <a href="{% url 'product_list' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i data-feather="box" class="me-2"></i>
                                        <span>Produits</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% if user|has_perm:"categories" %}
                                <li class="{% if request.path == '/stock/categories/' %}active{% endif %}">
                                    <a href="{% url 'categories_list' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-table fs-16 me-2"></i>
                                        <span>Categories</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% if user|has_perm:"expired_products" %}
                                <li class="{% if request.path == '/stock/expired-products/' %}active{% endif %}">
                                    <a href="{% url 'expired_products' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-exclamation-triangle fs-16 me-2"></i>
                                        <span>Produit expirer</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% if user|has_perm:"out_of_stock" %}
                                <li class="{% if request.path == '/stock/out-of-stock/' %}active{% endif %}">
                                    <a href="{% url 'out_of_stock_products' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-graph-up-arrow fs-16 me-2"></i>
                                        <span>Rupture de stcok</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% if user|has_perm:"stock_reports" %}
                                <li class="{% if request.path == '/fiche-de-stcok/' %}active{% endif %}">
                                    <a href="{% url 'stock_report' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-stack fs-16 me-2"></i>
                                        <span>fiches de Stock</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% comment %} <li class="{% if request.path == '/manage-stocks/' %}active{% endif %}">
                                    <a href="manage-stocks.html" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-stack fs-16 me-2"></i>
                                        <span>Gestions de Stock</span>
                                    </a>
                                </li>
                                <li class="{% if request.path == '/stock-adjustment/' %}active{% endif %}">
                                    <a href="stock-adjustment.html" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-arrow-up fs-16 me-2"></i>
                                        <span>Adjustment du stock</span>
                                    </a>
                                </li>
                                <li class="{% if request.path == '/stock-transfer/' %}active{% endif %}">
                                    <a href="stock-transfer.html" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-stack fs-16 me-2"></i>
                                        <span>Transferts</span>
                                    </a>
                                </li> {% endcomment %}
                            </ul>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Section Ventes -->
                {% if user|can_access:"sales" %}
                <div class="accordion-item border-0">
                    <h2 class="accordion-header" id="salesHeading">
                        <button class="accordion-button collapsed bg-transparent border-0 p-0 submenu-hdr" type="button"
                                data-bs-toggle="collapse" data-bs-target="#salesCollapse"
                                aria-expanded="{% if request.path == '/sales/sale/report/' or 'invoice-templates' in request.path or 'returns' in request.path %}true{% else %}false{% endif %}"
                                aria-controls="salesCollapse">
                            <i class="bi bi-cart fs-16 me-2"></i>
                            <span>Ventes</span>
                        </button>
                    </h2>
                    <div id="salesCollapse" class="accordion-collapse collapse {% if request.path == '/sales/sale/report/' or 'invoice-templates' in request.path or 'returns' in request.path %}show{% endif %}"
                         aria-labelledby="salesHeading" data-bs-parent="#sidebarAccordion">
                        <div class="accordion-body p-0">
                            <ul class="list-unstyled">
                                {% if user|has_perm:"sales" %}
                                <li class="{% if request.path == '/sales/sale/report/' %}active{% endif %}">
                                    <a href="{% url 'sale_report' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-receipt fs-16 me-2"></i>
                                        <span>Registre des vente</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% if user|has_perm:"invoice_templates" %}
                                <li class="{% if 'invoice-templates' in request.path %}active{% endif %}">
                                    <a href="{% url 'invoice_templates_list' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-file-text fs-16 me-2"></i>
                                        <span>Modèles de facture</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% if user|has_perm:"returns" %}
                                <li class="{% if 'returns' in request.path %}active{% endif %}">
                                    <a href="{% url 'returns_list' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-arrow-return-left fs-16 me-2"></i>
                                        <span>Retours de vente</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% comment %} <li class="{% if request.path == '/sales/sale/canceled/report/' %}active{% endif %}">
                                    <a href="{% url 'sale_canceled_report' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-files fs-16 me-2"></i>
                                        <span>Ventes annulées</span>
                                    </a>
                                </li> {% endcomment %}
                            </ul>
                        </div>
                    </div>
                </div>
                {% endif %}
                {% comment %} <!-- Section Réduction et promotion -->
                <div class="accordion-item border-0">
                    <h2 class="accordion-header" id="promotionHeading">
                        <button class="accordion-button collapsed bg-transparent border-0 p-0 submenu-hdr" type="button"
                                data-bs-toggle="collapse" data-bs-target="#promotionCollapse"
                                aria-expanded="false" aria-controls="promotionCollapse">
                            <i class="bi bi-ticket-perforated fs-16 me-2"></i>
                            <span>Réduction et promotion</span>
                        </button>
                    </h2>
                    <div id="promotionCollapse" class="accordion-collapse collapse"
                         aria-labelledby="promotionHeading" data-bs-parent="#sidebarAccordion">
                        <div class="accordion-body p-0">
                            <ul class="list-unstyled">
                                <li class="{% if request.path == '/coupons/' %}active{% endif %}">
                                    <a href="coupons.html" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-ticket-perforated fs-16 me-2"></i>
                                        <span>Reductions sur les ventes</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div> {% endcomment %}

                <!-- Section Achat -->
                {% if user|can_access:"purchases" %}
                <div class="accordion-item border-0">
                    <h2 class="accordion-header" id="purchaseHeading">
                        <button class="accordion-button collapsed bg-transparent border-0 p-0 submenu-hdr" type="button"
                                data-bs-toggle="collapse" data-bs-target="#purchaseCollapse"
                                aria-expanded="{% if request.path == '/purchase-order-report/' or request.path == '/purchase/' or '/purchases/invoices/' in request.path %}true{% else %}false{% endif %}"
                                aria-controls="purchaseCollapse">
                            <i class="bi bi-bag fs-16 me-2"></i>
                            <span>Achat</span>
                        </button>
                    </h2>
                    <div id="purchaseCollapse" class="accordion-collapse collapse {% if request.path == '/purchase-order-report/' or request.path == '/purchase/' or '/purchases/invoices/' in request.path %}show{% endif %}"
                         aria-labelledby="purchaseHeading" data-bs-parent="#sidebarAccordion">
                        <div class="accordion-body p-0">
                            <ul class="list-unstyled">
                                {% if user|has_perm:"purchases" %}
                                <li class="{% if request.path == '/purchase-order-report/' %}active{% endif %}">
                                    <a href="{% url 'purchase_registry' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-file-earmark-text fs-16 me-2"></i>
                                        <span>Registre des achats</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% if user|has_perm:"purchases" %}
                                <li class="{% if request.path == '/purchase/' %}active{% endif %}">
                                    <a href="{% url 'purchase' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-upload fs-16 me-2"></i>
                                        <span>Nouveau achat</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% if user|has_perm:"purchase_invoices" %}
                                <li class="{% if '/purchases/invoices/' in request.path %}active{% endif %}">
                                    <a href="{% url 'purchase_invoices' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-file-text fs-16 me-2"></i>
                                        <span>Consultation des factures</span>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </div>
                    </div>
                </div>
                {% endif %}
                <!-- Section Finances & Comptabilité -->
                {% if user|can_access:"finance" %}
                <div class="accordion-item border-0">
                    <h2 class="accordion-header" id="financeHeading">
                        <button class="accordion-button collapsed bg-transparent border-0 p-0 submenu-hdr" type="button"
                                data-bs-toggle="collapse" data-bs-target="#financeCollapse"
                                aria-expanded="{% if request.path == '/caisse/expenses/' or request.path == '/account-list/' or request.path == '/cash-flow/' %}true{% else %}false{% endif %}"
                                aria-controls="financeCollapse">
                            <i class="bi bi-calculator fs-16 me-2"></i>
                            <span>Finances & Comptabilité</span>
                        </button>
                    </h2>
                    <div id="financeCollapse" class="accordion-collapse collapse {% if request.path == '/caisse/expenses/' or request.path == '/account-list/' or request.path == '/cash-flow/' %}show{% endif %}"
                         aria-labelledby="financeHeading" data-bs-parent="#sidebarAccordion">
                        <div class="accordion-body p-0">
                            <ul class="list-unstyled">
                                {% comment %} <li class="submenu">
                                    <a href="#" class="d-flex align-items-center justify-content-between text-decoration-none p-2">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-files fs-16 me-2"></i>
                                            <span>Dépenses</span>
                                        </div>
                                        <span class="menu-arrow"></span>
                                    </a>
                                    <ul class="list-unstyled ms-3">
                                        <li class="{% if request.path == '/caisse/expenses/' %}active{% endif %}">
                                            <a href="{% url 'expense_list' %}" class="d-flex align-items-center text-decoration-none p-2">
                                                <span>Dépenses</span>
                                            </a>
                                        </li>
                                        <li class="{% if request.path == '/caisse/expenses/categories/' %}active{% endif %}">
                                            <a href="{% url 'expense_categories' %}" class="d-flex align-items-center text-decoration-none p-2">
                                                <span>Categorie de dépense</span>
                                            </a>
                                        </li>
                                    </ul>
                                </li> {% endcomment %}
                                {% if user|has_perm:"expenses" %}
                                <li class="{% if request.path == '/caisse/expenses/' %}active{% endif %}">
                                    <a href="{% url 'expense_list' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-files fs-16 me-2"></i>
                                        <span>Dépenses</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% if user|has_perm:"bank_accounts" %}
                                <li class="{% if request.path == '/account-list/' %}active{% endif %}">
                                    <a href="{% url 'bank_account_list' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-bank fs-16 me-2"></i>
                                        <span>Compte Bancaire</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% load operator_permissions %}
                                {% if user.role != 'SELLER' %}
                                <li class="{% if 'operators' in request.path %}active{% endif %}">
                                    <a href="{% url 'operator_account_list' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-person-badge fs-16 me-2"></i>
                                        <span>Comptes Exploitants</span>
                                    </a>
                                </li>
                                {% endif %}
                                {% comment %} <li class="{% if request.path == '/account-list/' %}active{% endif %}">
                                    <a href="account-list.html" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-bank fs-16 me-2"></i>
                                        <span>Etat de rapprochement</span>
                                    </a>
                                </li> {% endcomment %}
                                {% if user|has_perm:"cash_register" %}
                                <li class="{% if request.path == '/cash-flow/' %}active{% endif %}">
                                    <a href="{% url 'cash_register_list' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-cash-coin fs-16 me-2"></i>
                                        <span>Registre des caisse</span>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </div>
                    </div>
                </div>
                {% endif %}
                {% comment %} <!-- Section Rapports -->
                <div class="accordion-item border-0">
                    <h2 class="accordion-header" id="reportsHeading">
                        <button class="accordion-button collapsed bg-transparent border-0 p-0 submenu-hdr" type="button"
                                data-bs-toggle="collapse" data-bs-target="#reportsCollapse"
                                aria-expanded="false" aria-controls="reportsCollapse">
                            <i class="bi bi-file-earmark-bar-graph fs-16 me-2"></i>
                            <span>Rapports</span>
                        </button>
                    </h2>
                    <div id="reportsCollapse" class="accordion-collapse collapse"
                         aria-labelledby="reportsHeading" data-bs-parent="#sidebarAccordion">
                        <div class="accordion-body p-0">
                            <ul class="list-unstyled">
                                <li class="{% if request.path == '/expense-report/' %}active{% endif %}">
                                    <a href="expense-report.html" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-file-earmark-bar-graph fs-16 me-2"></i>
                                        <span>Report sur les depenses</span>
                                    </a>
                                </li>
                                <li class="{% if request.path == '/income-report/' %}active{% endif %}">
                                    <a href="income-report.html" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-graph-up fs-16 me-2"></i>
                                        <span>Situations des ventes</span>
                                    </a>
                                </li>
                                <li class="{% if request.path == '/tax-reports/' %}active{% endif %}">
                                    <a href="tax-repauports.html" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-graph-down fs-16 me-2"></i>
                                        <span>Raport de la tva</span>
                                    </a>
                                </li>
                                <li class="{% if request.path == '/smt-report/' %}active{% endif %}">
                                    <a href="{% url 'smt_report' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-pie-chart fs-16 me-2"></i>
                                        <span>SMT</span>
                                    </a>
                                </li>
                                <li class="{% if request.path == '/reports/tva/' %}active{% endif %}">
                                    <a href="{% url 'tva_report' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-receipt fs-16 me-2"></i>
                                        <span>Rapport TVA</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div> {% endcomment %}

                <!-- Section Gestion des utilisateurs -->
                {% if user|can_access:"users" %}
                <div class="accordion-item border-0">
                    <h2 class="accordion-header" id="usersHeading">
                        <button class="accordion-button collapsed bg-transparent border-0 p-0 submenu-hdr" type="button"
                                data-bs-toggle="collapse" data-bs-target="#usersCollapse"
                                aria-expanded="{% if request.path == '/users/' %}true{% else %}false{% endif %}"
                                aria-controls="usersCollapse">
                            <i class="bi bi-people fs-16 me-2"></i>
                            <span>Gestion des utilisateurs</span>
                        </button>
                    </h2>
                    <div id="usersCollapse" class="accordion-collapse collapse {% if request.path == '/users/' %}show{% endif %}"
                         aria-labelledby="usersHeading" data-bs-parent="#sidebarAccordion">
                        <div class="accordion-body p-0">
                            <ul class="list-unstyled">
                                {% if user|has_perm:"users" %}
                                <li class="{% if request.path == '/users/' %}active{% endif %}">
                                    <a href="{% url 'accounts:user_list' %}" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-shield-check fs-16 me-2"></i>
                                        <span>Utilisateurs</span>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </div>
                    </div>
                </div>
                {% endif %}

                {% comment %} <!-- Section Paramètres -->
                <div class="accordion-item border-0">
                    <h2 class="accordion-header" id="settingsHeading">
                        <button class="accordion-button collapsed bg-transparent border-0 p-0 submenu-hdr" type="button"
                                data-bs-toggle="collapse" data-bs-target="#settingsCollapse"
                                aria-expanded="false" aria-controls="settingsCollapse">
                            <i class="bi bi-gear fs-16 me-2"></i>
                            <span>Paramètres</span>
                        </button>
                    </h2>
                    <div id="settingsCollapse" class="accordion-collapse collapse"
                         aria-labelledby="settingsHeading" data-bs-parent="#sidebarAccordion">
                        <div class="accordion-body p-0">
                            <ul class="list-unstyled">
                                <li class="submenu">
                                    <a href="#" class="d-flex align-items-center justify-content-between text-decoration-none p-2">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-gear fs-16 me-2"></i>
                                            <span>Paramètre General</span>
                                        </div>
                                        <span class="menu-arrow"></span>
                                    </a>
                                    <ul class="list-unstyled ms-3">
                                        <li class="{% if request.path == '/general-settings/' %}active{% endif %}">
                                            <a href="general-settings.html" class="d-flex align-items-center text-decoration-none p-2">
                                                <span>Paramètre</span>
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div> {% endcomment %}

                {% comment %} <!-- Section Aide -->
                <div class="accordion-item border-0">
                    <h2 class="accordion-header" id="helpHeading">
                        <button class="accordion-button collapsed bg-transparent border-0 p-0 submenu-hdr" type="button"
                                data-bs-toggle="collapse" data-bs-target="#helpCollapse"
                                aria-expanded="false" aria-controls="helpCollapse">
                            <i class="bi bi-question-circle fs-16 me-2"></i>
                            <span>Aide</span>
                        </button>
                    </h2>
                    <div id="helpCollapse" class="accordion-collapse collapse"
                         aria-labelledby="helpHeading" data-bs-parent="#sidebarAccordion">
                        <div class="accordion-body p-0">
                            <ul class="list-unstyled">
                                <li class="{% if request.path == '/documentation/' %}active{% endif %}">
                                    <a href="#" class="d-flex align-items-center text-decoration-none p-2">
                                        <i class="bi bi-file-text fs-16 me-2"></i>
                                        <span>Documentation</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div> {% endcomment %}

                {% comment %} <!-- Menu Profil Utilisateur -->
                <div class="menu-item border-0 mb-2">
                    <a href="{% url 'accounts:profile' %}" class="d-flex align-items-center text-decoration-none p-2">
                        <i class="bi bi-person-circle me-2"></i>
                        <span>Profil</span>
                    </a>
                </div> {% endcomment %}

            </div> <!-- Fin de l'accordéon -->
        </div>
    </div>
</div>
<!-- /Sidebar -->

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gestion des sous-menus dans l'accordéon
    const submenuLinks = document.querySelectorAll('#sidebarAccordion .submenu > a');

    submenuLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const parentLi = this.parentElement;
            const submenuUl = parentLi.querySelector('ul');

            if (submenuUl) {
                // Toggle du sous-menu
                if (submenuUl.style.display === 'none' || submenuUl.style.display === '') {
                    submenuUl.style.display = 'block';
                    parentLi.classList.add('active');
                } else {
                    submenuUl.style.display = 'none';
                    parentLi.classList.remove('active');
                }
            }
        });
    });

    // Ouvrir automatiquement les sous-menus contenant des éléments actifs
    const activeItems = document.querySelectorAll('#sidebarAccordion .accordion-body ul li.active');
    activeItems.forEach(function(activeItem) {
        const parentSubmenu = activeItem.closest('.submenu');
        if (parentSubmenu) {
            const submenuUl = parentSubmenu.querySelector('ul');
            if (submenuUl) {
                submenuUl.style.display = 'block';
                parentSubmenu.classList.add('active');
            }
        }
    });

    // Compatibilité avec le mode mini-sidebar
    const toggleBtn = document.getElementById('toggle_btn');
    if (toggleBtn) {
        toggleBtn.addEventListener('click', function() {
            setTimeout(function() {
                // Réinitialiser l'état des accordéons en mode mini
                if (document.body.classList.contains('mini-sidebar')) {
                    const openAccordions = document.querySelectorAll('#sidebarAccordion .accordion-collapse.show');
                    openAccordions.forEach(function(accordion) {
                        if (!document.body.classList.contains('expand-menu')) {
                            // Fermer les accordéons en mode mini
                            const bsCollapse = new bootstrap.Collapse(accordion, {
                                toggle: false
                            });
                            bsCollapse.hide();
                        }
                    });
                }
            }, 150);
        });
    }

    // Gestion du hover en mode mini-sidebar
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
        sidebar.addEventListener('mouseenter', function() {
            if (document.body.classList.contains('mini-sidebar')) {
                // Rouvrir l'accordéon actif quand on survole en mode mini
                const currentPath = window.location.pathname;
                const accordions = document.querySelectorAll('#sidebarAccordion .accordion-collapse');

                accordions.forEach(function(accordion) {
                    const activeLinks = accordion.querySelectorAll('a[href="' + currentPath + '"]');
                    if (activeLinks.length > 0) {
                        const bsCollapse = new bootstrap.Collapse(accordion, {
                            toggle: false
                        });
                        bsCollapse.show();
                    }
                });
            }
        });

        sidebar.addEventListener('mouseleave', function() {
            if (document.body.classList.contains('mini-sidebar') && !document.body.classList.contains('expand-menu')) {
                // Fermer les accordéons quand on quitte la sidebar en mode mini
                const openAccordions = document.querySelectorAll('#sidebarAccordion .accordion-collapse.show');
                openAccordions.forEach(function(accordion) {
                    const bsCollapse = new bootstrap.Collapse(accordion, {
                        toggle: false
                    });
                    bsCollapse.hide();
                });
            }
        });
    }

    // Préserver l'état de l'accordéon dans le localStorage
    const accordionButtons = document.querySelectorAll('#sidebarAccordion .accordion-button');
    accordionButtons.forEach(function(button) {
        const target = button.getAttribute('data-bs-target');
        const accordionId = target.replace('#', '');

        // Restaurer l'état depuis localStorage
        const savedState = localStorage.getItem('sidebar_accordion_' + accordionId);
        if (savedState === 'open') {
            const targetElement = document.querySelector(target);
            if (targetElement && !targetElement.classList.contains('show')) {
                const bsCollapse = new bootstrap.Collapse(targetElement, {
                    toggle: false
                });
                bsCollapse.show();
            }
        }

        // Sauvegarder l'état lors des changements
        button.addEventListener('click', function() {
            setTimeout(function() {
                const targetElement = document.querySelector(target);
                const isOpen = targetElement.classList.contains('show');
                localStorage.setItem('sidebar_accordion_' + accordionId, isOpen ? 'open' : 'closed');
            }, 100);
        });
    });
});
</script>
